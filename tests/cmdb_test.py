# -*- coding: utf-8 -*-
# !/usr/bin/python

import os
import sys

reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
test_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(test_path)      # ../
resource_path = root_path + '/resources'    # ../resources
utils_path = root_path + '/utils'           # ../utils

sys.path.append(root_path)
sys.path.append(resource_path)
sys.path.append(utils_path)

from utils import CMDBApi
from resources.config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host

cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

class DevManager:
    def __init__(self, name):
        self.name = name
        self.list_a = []
        self.list_b = []

class DevMatcher:
    def __init__(self, name_list):
        self.manager_dict = {}
        for name in name_list:
            self.manager_dict[name] = DevManager(name)

        self.managerA_not_found = 0
        self.managerB_not_found = 0

    def distribute_devices(self, all_devices):
        for dev in all_devices:
            if dev['managerA'] not in self.manager_dict.keys():                
                print('unexpected managerA:{} for dev:{}',format(dev['managerA'], dev['ip']))
                self.managerA_not_found += 1
            else:
                self.manager_dict[dev['managerA']].list_a.append(dev['ip'])

            if dev['managerB'] not in self.manager_dict.keys():
                print('unexpected managerB:{} for dev:{}',format(dev['managerB'], dev['ip']))
                self.managerB_not_found += 1
            else:
                self.manager_dict[dev['managerB']].list_b.append(dev['ip'])

    def error_report(self):
        print('managerA_not_found: {}'.format(self.managerA_not_found))
        print('managerB_not_found: {}'.format(self.managerB_not_found))

if __name__ == '__main__':

    manager_list = [u'陈雷', u'蔡炯桐', u'廖定锋', u'张熠', u'冯喆', u'陈导', u'陈爱华', u'朱旻']

    matcher = DevMatcher(manager_list)
    
    all_device_data = cmdb_api.get_all_data(
        query = {
            "status": {"$eq": "运行中"},
            "userGroupManager": "服务器及存储基础设施组",
            "$or": [
                {"osDistro": {"$like": '%red%'}},
                {"osDistro": {"$like": '%centos%'}},
                {"osDistro": {"$like": '%Kylin%'}}
            ],
        }
    )

    matcher.distribute_devices(all_device_data)
    matcher.error_report()
    # all_device_data = all_device_data[0:50]
    # ips = [device["ip"] for device in all_device_data]
    
