# !/usr/bin/python2
# -*- coding: utf-8 -*-
import datetime
import json
import time

import jwt
import requests
import urllib3

from utils import _decode_dict

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)



class HuaweiCloudSpider:
    def __init__(self, address, auth_user, auth_key, farmId):
        self.address = address
        self.auth_user = auth_user
        self.auth_key = auth_key
        self.farmID = farmId
        self._token = self.get_token()

    def get_token(self):
        response = requests.post(
            url='https://{}/services/ita/login'.format(self.address),
            headers={
                'Host': self.address,
                'Content-Type': 'application/json;charset=UTF-8',
                'Accept': 'application/json;charset=UTF-8 ',
                'Accept-Language': 'zh_CN',
                'X-Auth-User': self.auth_user,
                'X-Auth-Key': self.auth_key
            },
            verify=False)
        if not response or not response.ok:
            print(response.status_code)
            raise Exception('Can not get Token')
        return response.headers['X-Auth-Token']

    def _get_connection_info(self, start_time, end_time):
        print('Get Info From {} To {}'.format(start_time, end_time))
        payload_data = {
            'operatorId': 'admin',
            'flag': 1,
            'farmId': self.farmID,
            'startNum': 0,
            'infoSize': 1000000,
            'startTime': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'endTime': end_time.strftime('%Y-%m-%d %H:%M:%S')
        }
        if not self.farmID:
            payload_data.pop('farmId')
        response = requests.post(
            url='https://{}/services/ita/describeConnectionInfo'.format(self.address),
            data=json.dumps(payload_data),
            headers={
                'Host': self.address,
                'Content-Type': 'application/json;charset=UTF-8 ',
                'Accept': 'application/json;charset=UTF-8 ',
                'Accept-Language': 'zh_CN',
                'X-Auth-Token': self._token
            },
            verify=False
        )
        if not response or not response.ok:
            print(response.status_code)
            raise Exception('Not response')
        if not response.json().get('userConnectVMInfo'):
            raise Exception('Not userConnectVMInfo')
        return response.json().get('userConnectVMInfo')

    def get_connection_info(self, start_time, end_time, delta=7):
        res = []
        delta = datetime.timedelta(days=delta)
        cur_s = start_time
        while cur_s + delta < end_time:
            res.extend(self._get_connection_info(cur_s, cur_s + delta))
            cur_s = cur_s + delta
        res.extend(self._get_connection_info(cur_s, end_time))
        return res


class ITEMEmployeeExporter:
    def __init__(self, url, access_key, secret_key, ):
        self.url = url
        self.access_key = access_key
        self.secret_key = secret_key

    @staticmethod
    def get_timedelta(expireTime):
        unit = expireTime[-1].lower()
        num = int(expireTime[:-1])
        if unit == 'd':
            return datetime.timedelta(days=num)
        elif unit == 'h':
            return datetime.timedelta(hours=num)
        elif unit == 'm':
            return datetime.timedelta(minutes=num)
        elif unit == 's':
            return datetime.timedelta(seconds=num)

    @staticmethod
    def getTokenJwtHS256(accessKey, secretKey, expireTime):
        now = datetime.datetime.now()
        exp = now + ITEMEmployeeExporter.get_timedelta(expireTime)
        payload = {
            u'iss': accessKey,
            u'exp': int(time.mktime(exp.timetuple())),
            u'iat': int(time.mktime(now.timetuple())),
            u'appCode': u'OpeDep'
        }
        token = jwt.encode(payload=payload, key=secretKey, algorithm='HS256')
        return token

    def get_oth_user_info(self):
        access_key = self.access_key.decode()
        secret_key = self.secret_key.decode()

        token = ITEMEmployeeExporter.getTokenJwtHS256(access_key, secret_key, '5m')
        res = None
        r = requests.get(
            url=self.url,
            headers={
                'Content-Type': "application/json",
                "Authorization": token,
            }
        )
        if not r.ok:
            raise Exception('Unable access user info')
        try:
            res = json.loads(r.content, object_hook=_decode_dict)['data']
        except Exception:
            raise Exception('Unable loads content')
        finally:
            return res
