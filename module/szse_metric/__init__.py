# -*- coding: utf-8 -*-
# !/usr/bin/python
import json
import time

import requests


class BaseSZSEMetric:
    def __init__(self, name, value, id, monitorType, metricType, ip, appId, dataTime,
                 tag0=None, tag1=None, tag2=None, tag3=None, tag4=None,
                 tag5=None, tag6=None, tag7=None, tag8=None, tag9=None,
                 category=None, collectTime=None, createDate=None):
        self.name = name
        self.value = value
        self.id = id
        self.monitorType = monitorType
        self.metricType = metricType
        self.ip = ip
        self.appId = appId
        self.tag0 = tag0
        self.tag1 = tag1
        self.tag2 = tag2
        self.tag3 = tag3
        self.tag4 = tag4
        self.tag5 = tag5
        self.tag6 = tag6
        self.tag7 = tag7
        self.tag8 = tag8
        self.tag9 = tag9
        self.category = category
        self.collectTime = collectTime
        self.dataTime = dataTime
        self.createDate = createDate
        self.timestamp = int(time.time())

    def __repr__(self):
        return '{} {} {} {} {}'.format(self.name, self.value, self.ip, self.appId, self.id)


class SZSEMetricHandler:
    def __init__(self, url='http://************:8080/ems/metric/collectMetrics'):
        self.url = url

    def send_metrics(self, metrics):
        headers = {
            'Content-Type': "application/json"
        }
        metrics_data = []
        for metric in metrics:
            tags_d = {
                'ip': metric.ip,
                'appId': metric.appId,
                'metricType': metric.metricType,
                'monitorType': metric.monitorType,
                'dataTime': metric.dataTime,
                'collectTime': metric.collectTime,
                'tag0': metric.tag0,
                'tag1': metric.tag1,
                'tag2': metric.tag2,
                'tag3': metric.tag3,
                'tag4': metric.tag4,
                'tag5': metric.tag5,
                'tag6': metric.tag6,
                'tag7': metric.tag7,
                'tag8': metric.tag8,
                'tag9': metric.tag9,
            }
            for k, v in tags_d.items():
                if v and ' ' in v:
                    tags_d[k] = v.replace(r'\"', '')
            cur_data = {
                'name': metric.name,
                'value': metric.value,
                'timestamp': metric.timestamp,
                'tags': json.dumps(tags_d),
                'instance': json.dumps({
                    'id': metric.id
                })
            }
            metrics_data.append(cur_data)
        r = requests.post(url=self.url, data=json.dumps(metrics_data), headers=headers)
        if not r.ok:
            print(r.content)
