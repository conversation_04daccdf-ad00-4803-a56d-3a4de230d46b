# -*- coding: utf-8 -*-
# !/usr/bin/python
import datetime
import re
from multiprocessing.pool import ThreadPool

import requests


class DeviceSNMPPrometheusMetricSpider:

    def __init__(self, exporter_address='*************:9116'):
        self.url_format = 'http://' + exporter_address + '/snmp?module={}&target={}'

    def get_exporter_responses(self, module, devices, pool_size=None):
        if pool_size:
            self.pool = ThreadPool(pool_size)
            pool_map_result = self.pool.map_async(
                func=requests.get,
                iterable=[self.url_format.format(module, device.DeviceManagementAddress) for device in devices]
            )
            return pool_map_result.get()
        else:
            res = []
            for url in [self.url_format.format(module, device.DeviceManagementAddress) for device in devices]:
                r = requests.get(url)
                res.append(r)
            return res


class PrometheusMetric:
    def __init__(self, name, value, tags={}):
        self.name = name
        self.value = value
        self.tags = tags

    def __str__(self):
        return '{}:{}, tags={}'.format(self.name, self.value, str(self.tags))


def cal_datetime(interval):
    now = datetime.datetime.now()
    if now.minute % interval == 0:
        now = now - datetime.timedelta(seconds=now.second) - datetime.timedelta(microseconds=now.microsecond)
    else:
        now = now - datetime.timedelta(minutes=now.minute % interval) - datetime.timedelta(
            seconds=now.second) - datetime.timedelta(microseconds=now.microsecond)
    return str(now)


def parse_prometheus_webpage_metric_string(string):
    if not string or string[0] == '#':
        return None
    if '{' not in string and '}' not in string and len(string.split(' ')) == 2:
        name = string.split(' ')[0]
        value = string.split(' ')[1]
        if name in {
            'snmp_scrape_duration_seconds',
            'snmp_scrape_pdus_returned',
            'snmp_scrape_walk_duration_seconds'
        }:
            return None
        return PrometheusMetric(name=name, value=value)
    else:
        name = string[:string.index('{')]
        value = string.split(' ')[-1]
        tags_string = string[string.index('{') + 1:string.index('}')] + ','
        tags = [a.split('=') for a in tags_string.split('",') if a]
        tags = dict([(a[0].strip(), a[1][1:]) for a in tags])
        return PrometheusMetric(
            name=name,
            value=value,
            tags=tags
        )

    # if self.reg.match(string):
    #     tmp_data = self.reg.match(string).groupdict()
    #     keys = self.reg_tag_key.findall(tmp_data.get("tags"))
    #     values = self.reg_tag_value.findall(tmp_data.get("tags"))
    #     # tmp_data['tags'] = dict(zip(keys, values))
    #     return PrometheusMetric(name=tmp_data.get('name'),
    #                             value=tmp_data.get('value'),
    #                             tags=dict(zip(keys, values)))


def parse_prometheus_webpage(request_content):
    if request_content.split('\n')[0] == 'An error has occurred while serving metrics:':
        return None
    res = [parse_prometheus_webpage_metric_string(string) for string in request_content.split('\n')]
    return [a for a in res if a]


class Parser:
    def __init__(self):
        self.reg = re.compile(r'(?P<name>\w+)\{(?P<tags>[^\{\}]+)\} (?P<value>[\w.e+]+)')
        self.reg_tag_key = re.compile('\w+(?==)')
        self.reg_tag_value = re.compile('(?<==")[^"]*')
