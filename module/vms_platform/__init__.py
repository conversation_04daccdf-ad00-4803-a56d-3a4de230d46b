# -*- coding: utf-8 -*-
# !/usr/bin/python

import atexit
import json
import time
import traceback

import requests
import sqlalchemy
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
from pyVim.connect import SmartConnectNoSSL, Disconnect

from item import MYSQL_4226, CMDB_WRITE

start = time.clock()


def connect(host, user, pwd):
    service_instance = None
    try:
        service_instance = SmartConnectNoSSL(host=host, user=user, pwd=pwd)
        atexit.register(Disconnect, service_instance)
    except Exception as e:
        print(e)
        print(traceback.format_exc())
    if not service_instance:
        raise SystemExit("Unable to connect to host with supplied credentials.")
    return service_instance


def endit():
    """
    times how long it took for this script to run.

    :return:
    """
    end = time.clock()
    total = end - start
    print("Completion time: {0} seconds.".format(total))


class Vm_Location(CMDB_WRITE):
    __tablename__ = 'SZSE_CMDB_VM_Loaction'
    __table_args__ = {'schema': 'dbo'}
    Submitter = sqlalchemy.Column(sqlalchemy.String(length=64))
    VMName = sqlalchemy.Column(sqlalchemy.String(length=64), nullable=False)
    Site = sqlalchemy.Column(sqlalchemy.String(length=64), nullable=False)
    IPAddress = sqlalchemy.Column(sqlalchemy.String(length=64))
    Request_ID = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True)
    Submitter = sqlalchemy.Column(sqlalchemy.String(length=64))

    Assigned_To = sqlalchemy.Column(sqlalchemy.String(length=64))
    Last_Modified_By = sqlalchemy.Column(sqlalchemy.String(length=64))
    Create_Date = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    Modified_Date = sqlalchemy.Column(sqlalchemy.String(length=64))
    Status = sqlalchemy.Column(sqlalchemy.INTEGER)
    Short_Description = sqlalchemy.Column(sqlalchemy.String(length=64))

    def __str__(self):
        return 'VMName:{} IPAddress:{} Site:{}'.format(self.VMName, self.IPAddress, self.Site)

    def __repr__(self):
        return self.__str__()


class VitrualMachine:
    __tablename__ = 'tbl_vm'
    # id = sqlalchemy.Column(sqlalchemy.String(length=32), primary_key=True, nullable=False)
    name = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True, nullable=False)
    virtualization_platform = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    cmdb_building = sqlalchemy.Column(sqlalchemy.String(length=100))
    guestId = sqlalchemy.Column(sqlalchemy.String(length=48))
    guestFullName = sqlalchemy.Column(sqlalchemy.String(length=64))
    host = sqlalchemy.Column(sqlalchemy.String(length=32))
    state = sqlalchemy.Column(sqlalchemy.String(length=32))
    guestHostName = sqlalchemy.Column(sqlalchemy.String(length=100))
    guestIP = sqlalchemy.Column(sqlalchemy.String(length=100))
    uuid = sqlalchemy.Column(sqlalchemy.String(length=48), primary_key=True)
    modified_date = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    __mapper_args__ = {
        'polymorphic_on': virtualization_platform
    }

    def __repr__(self):
        return self.__str__()

    def __str__(self):
        return 'name:{}\thost:{}\tguestIP:{}'.format(self.name, self.host, self.guestIP)


class Vmware_VM(VitrualMachine, MYSQL_4226):
    __mapper_args__ = {
        'polymorphic_identity': 'VMware',
    }

    template = sqlalchemy.Column(sqlalchemy.String(length=16))
    vmPathName = sqlalchemy.Column(sqlalchemy.String(length=128))
    memorySizeMB = sqlalchemy.Column(sqlalchemy.Integer)
    cpuReservation = sqlalchemy.Column(sqlalchemy.Integer)
    memoryReservation = sqlalchemy.Column(sqlalchemy.Integer)
    numCpu = sqlalchemy.Column(sqlalchemy.Integer)
    numEthernetCards = sqlalchemy.Column(sqlalchemy.Integer)
    numVirtualDisks = sqlalchemy.Column(sqlalchemy.Integer)
    instanceUuid = sqlalchemy.Column(sqlalchemy.String(length=48))
    annotation = sqlalchemy.Column(sqlalchemy.String(length=1024))
    hwVersion = sqlalchemy.Column(sqlalchemy.String(length=16))


class Openstack_VM(VitrualMachine, MYSQL_4226):
    __mapper_args__ = {
        'polymorphic_identity': 'Openstack',
    }


class OpenstackConnection:
    def __init__(self, host, user, password):
        self.os_username = user
        self.os_password = password
        self.host = host

        self.os_user_domain_name = "Default"
        self.os_project_domain_name = "default"

        self.os_project_name = 'admin'
        self.token = self._get_token()
        self.headers = {'X-Auth-Token': self.token}

    def _get_token(self):
        headers = {'Content-type': 'application/json'}
        data = {
            "auth": {
                "identity": {
                    "methods": ["password"],
                    "password": {
                        "user": {
                            "domain": {
                                "name": self.os_user_domain_name
                            },
                            "name": self.os_username,
                            "password": self.os_password
                        }

                    }
                },
            }
        }
        token_requests = requests.post('https://{}:5000/v3/auth/tokens?nocatalog'.format(self.host), headers=headers,
                                       data=json.dumps(data), verify=False)
        return token_requests.headers.get("X-Subject-Token")

    def get_vms(self):
        res = []
        response = requests.get('https://{}:8774/v2.1/servers?all_tenants=true&acitve=true'.format(self.host),
                                headers={'X-Auth-Token': self.token},
                                verify=False)
        if not response.ok:
            # todo log
            print('Erro')
            return None
        origin_dict = response.json()
        for vm_d in origin_dict.get('servers'):
            address = set()
            name = vm_d.get("name", "default")
            # !
            host = 'ft-default'
            vm_detail = requests.get(
                [link.get("href") for link in vm_d.get('links') if link["rel"] == 'self'][0],
                headers={'X-Auth-Token': self.token},
                verify=False).json()

            if 'computeFault' in vm_detail:
                # print("{} has computeFault".format(vm_d.get("name")))
                # print(vm_detail)
                pass

            if vm_detail.get('server'):
                for net in vm_detail.get('server').get("addresses").data():
                    for net_addr in net:
                        address.add(net_addr.get('addr'))
                name = vm_detail.get('server').get("name")
                host = vm_detail.get('server').get("OS-EXT-SRV-ATTR:host")

            address = ','.join(sorted(list(address)))

            cur_openstack_vm = Openstack_VM()
            cur_openstack_vm.uuid = vm_d.get("id")
            cur_openstack_vm.guestId = None
            cur_openstack_vm.name = name
            cur_openstack_vm.guestHostName = name
            # ! todo 写死了 Mysql
            cur_openstack_vm.cmdb_building = '福田新大楼'
            cur_openstack_vm.guestIP = address
            cur_openstack_vm.state = "running"
            cur_openstack_vm.host = host
            res.append(cur_openstack_vm)
        return res
