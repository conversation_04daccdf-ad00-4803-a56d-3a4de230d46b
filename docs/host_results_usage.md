# 主机结果管理功能使用指南

## 概述

新的 `add_info` 函数已经优化，现在每个IP都有一个完整的任务执行历史列表。这个功能提供了更详细的任务跟踪和结果分析能力。

## 数据结构

### 1. 原有数据结构（向后兼容）
- `self.all_info`: 所有任务的完整列表
- `self.host`: 按状态分类的主机结果

### 2. 新增数据结构
- `self.host_results`: 每个IP的完整任务执行历史

```python
# 数据结构示例
self.host_results = {
    "*************": [
        {
            "exec_time": "2024-01-01 10:00:00",
            "host_ip": "*************",
            "task_name": "Gather facts",
            "status": "ok",
            "result_info": {...},
            "play_name": "Basic Setup",
            "task_uuid": "uuid-string",
            "duration": 2.5
        },
        # 更多任务...
    ],
    "*************": [
        # 该主机的任务列表...
    ]
}
```

## 新增方法

### 1. `get_host_results(host_ip=None)`
获取指定主机或所有主机的完整任务执行历史。

```python
# 获取所有主机的结果
all_results = callback.get_host_results()

# 获取特定主机的结果
host_tasks = callback.get_host_results("*************")
```

### 2. `get_host_summary(host_ip)`
获取指定主机的执行摘要，包含各种状态的统计。

```python
summary = callback.get_host_summary("*************")
print(f"总任务数: {summary['total_tasks']}")
print(f"成功: {summary['ok']}")
print(f"失败: {summary['failed']}")
```

### 3. `get_all_hosts_summary()`
获取所有主机的执行摘要。

```python
all_summaries = callback.get_all_hosts_summary()
for host_ip, summary in all_summaries.items():
    print(f"主机 {host_ip}: {summary['total_tasks']} 个任务")
```

### 4. `get_tasks_by_status(host_ip, status)`
获取指定主机指定状态的任务。

```python
# 获取失败的任务
failed_tasks = callback.get_tasks_by_status("*************", "failed")

# 获取成功的任务
ok_tasks = callback.get_tasks_by_status("*************", "ok")
```

## 使用示例

### 基本使用

```python
from my_ansible import MyAnsibleService

# 创建服务实例
mas = MyAnsibleService(
    inventory="*************,*************",
    # 其他配置...
)

# 执行任务
mas.run(play_source)

# 获取回调对象
callback = mas.results_callback

# 查看每个主机的结果
for host_ip in ["*************", "*************"]:
    print(f"\n主机 {host_ip} 的结果:")
    
    # 获取摘要
    summary = callback.get_host_summary(host_ip)
    print(f"  成功任务: {summary['ok']}")
    print(f"  失败任务: {summary['failed']}")
    
    # 获取失败的任务详情
    failed_tasks = callback.get_tasks_by_status(host_ip, "failed")
    if failed_tasks:
        print("  失败任务详情:")
        for task in failed_tasks:
            print(f"    - {task['task_name']}: {task['result_info'].get('msg', 'No message')}")
```

### 高级分析

```python
def analyze_execution_results(callback):
    """分析执行结果"""
    
    # 1. 整体统计
    all_summaries = callback.get_all_hosts_summary()
    total_hosts = len(all_summaries)
    successful_hosts = sum(1 for s in all_summaries.values() if s['failed'] == 0)
    
    print(f"总主机数: {total_hosts}")
    print(f"完全成功的主机: {successful_hosts}")
    print(f"成功率: {(successful_hosts/total_hosts)*100:.1f}%")
    
    # 2. 找出问题主机
    problem_hosts = []
    for host_ip, summary in all_summaries.items():
        if summary['failed'] > 0 or summary['unreachable'] > 0:
            problem_hosts.append(host_ip)
    
    if problem_hosts:
        print(f"\n问题主机: {problem_hosts}")
        
        # 3. 分析具体问题
        for host_ip in problem_hosts:
            print(f"\n主机 {host_ip} 的问题:")
            failed_tasks = callback.get_tasks_by_status(host_ip, "failed")
            for task in failed_tasks:
                print(f"  失败任务: {task['task_name']}")
                print(f"  错误信息: {task['result_info'].get('msg', 'Unknown error')}")

# 使用示例
analyze_execution_results(mas.results_callback)
```

### 结果导出

```python
import json

def export_host_results(callback, filename):
    """导出主机结果到JSON文件"""
    
    export_data = {
        'timestamp': datetime.now().isoformat(),
        'summary': callback.get_all_hosts_summary(),
        'detailed_results': callback.get_host_results()
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    print(f"结果已导出到: {filename}")

# 导出结果
export_host_results(mas.results_callback, "ansible_results.json")
```

## 优势

1. **详细跟踪**: 每个主机的每个任务都有完整记录
2. **灵活查询**: 可以按主机、状态、任务名等多种方式查询
3. **统计分析**: 提供丰富的统计和摘要功能
4. **向后兼容**: 保持原有API不变
5. **易于扩展**: 可以轻松添加新的分析功能

## 注意事项

1. 新功能会增加内存使用，特别是在大规模部署时
2. 所有原有的方法和属性仍然可用
3. 新的 `host_results` 数据结构提供了更丰富的信息
4. 建议在处理大量主机时定期清理或导出结果

## 演示脚本

运行以下命令查看完整演示：

```bash
python scripts/demo_host_results.py
```

这个脚本展示了所有新功能的使用方法。
