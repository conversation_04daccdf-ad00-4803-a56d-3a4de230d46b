# Vault密码探测功能使用指南

## 概述

新增的密码探测功能可以自动检测ansible vault文件中保存的SSH密码是否仍然有效，并自动清理无效的连接信息。这有助于维护vault文件的准确性和减少连接失败。

## 功能特性

1. **并发连接测试**: 支持多线程并发测试，提高检测效率
2. **自动清理**: 自动删除无效的连接信息和空的主机条目
3. **详细报告**: 提供完整的探测结果报告
4. **安全备份**: 可选择在修改前创建备份文件
5. **灵活配置**: 支持自定义超时时间和并发数
6. **试运行模式**: 支持不保存更改的试运行模式

## Vault文件格式

支持的vault文件格式：
```json
{
  "*************": {
    "connections": [
      {"user": "root", "password": "password123"},
      {"user": "admin", "password": "admin123"}
    ]
  },
  "*************": {
    "connections": [
      {"user": "root", "password": "another_password"}
    ]
  }
}
```

## 使用方法

### 1. 编程接口

#### 基本使用
```python
from my_ansible import probe_vault_passwords

# 探测vault文件中的密码
results = probe_vault_passwords(
    vault_file_path='/path/to/vault_file',
    vault_password='your_vault_password',
    timeout=5,
    max_workers=10,
    save_changes=True
)

print(f"测试了 {results['tested']} 个连接")
print(f"删除了 {len(results['removed_connections'])} 个无效连接")
```

#### 高级使用
```python
from my_ansible import MyAnsibleVaultManager

# 创建vault管理器
vault_manager = MyAnsibleVaultManager('/path/to/vault_file', 'vault_password')
vault_manager.decrypt_file()

# 获取统计信息
stats = vault_manager.get_vault_statistics()
print(f"总主机数: {stats['total_hosts']}")
print(f"总连接数: {stats['total_connections']}")

# 执行密码探测
results = vault_manager.probe_vault_passwords(
    timeout=10,
    max_workers=5,
    save_changes=False  # 试运行模式
)

# 手动保存（如果需要）
if input("是否保存更改? (y/N): ").lower() == 'y':
    vault_manager.encrypt_file()
```

### 2. 命令行工具

#### 基本用法
```bash
# 基本探测
python scripts/vault_password_probe.py -f /path/to/vault_file -p vault_password

# 交互模式（提示输入密码）
python scripts/vault_password_probe.py -f /path/to/vault_file --interactive

# 试运行模式（不保存更改）
python scripts/vault_password_probe.py -f /path/to/vault_file -p vault_password --dry-run
```

#### 高级选项
```bash
# 自定义超时和并发数
python scripts/vault_password_probe.py -f vault_file -p password --timeout 10 --max-workers 20

# 创建备份并保存结果
python scripts/vault_password_probe.py -f vault_file -p password --backup --output results.json

# 仅分析vault文件（不测试连接）
python scripts/vault_password_probe.py -f vault_file -p password --analyze-only

# 详细输出
python scripts/vault_password_probe.py -f vault_file -p password --verbose
```

### 3. 集成到现有脚本

```python
# 在执行ansible任务前先探测密码
python scripts/example_ansible_script.py --with-probe
```

## 配置选项

### 探测参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `timeout` | 5 | SSH连接超时时间（秒） |
| `max_workers` | 10 | 最大并发连接数 |
| `save_changes` | True | 是否自动保存更改 |

### 命令行选项

| 选项 | 说明 |
|------|------|
| `-f, --file` | Vault文件路径（必需） |
| `-p, --password` | Vault密码 |
| `--interactive` | 交互模式，提示输入密码 |
| `--dry-run` | 试运行模式，不保存更改 |
| `--analyze-only` | 仅分析vault文件，不测试连接 |
| `--timeout` | SSH连接超时时间 |
| `--max-workers` | 最大并发连接数 |
| `--output` | 保存结果到JSON文件 |
| `--backup` | 在修改前创建备份 |
| `--verbose` | 详细输出 |

## 返回结果

探测函数返回详细的结果字典：

```python
{
    'tested': 15,                    # 测试的连接总数
    'reachable': 12,                 # 可达的连接数
    'unreachable': 3,                # 不可达的连接数
    'removed_connections': [         # 被删除的连接列表
        {'host_ip': '*************', 'user': 'admin'},
        # ...
    ],
    'valid_connections': [           # 有效的连接列表
        {'host_ip': '*************', 'user': 'root'},
        # ...
    ],
    'errors': [                      # 错误信息列表
        {'host_ip': '*************', 'user': 'test', 'error': 'Connection timeout'},
        # ...
    ]
}
```

## 最佳实践

### 1. 定期维护
```bash
# 创建定期维护脚本
#!/bin/bash
python scripts/vault_password_probe.py \
    -f /path/to/vault_file \
    -p "$VAULT_PASSWORD" \
    --backup \
    --output "probe_results_$(date +%Y%m%d).json" \
    --timeout 10
```

### 2. 批量处理
```python
import glob
from my_ansible import probe_vault_passwords

# 处理多个vault文件
vault_files = glob.glob('/path/to/vault_files/*.vault')
for vault_file in vault_files:
    print(f"处理文件: {vault_file}")
    results = probe_vault_passwords(vault_file, vault_password)
    print(f"清理了 {len(results['removed_connections'])} 个无效连接")
```

### 3. 集成到CI/CD
```yaml
# .github/workflows/vault-maintenance.yml
name: Vault Maintenance
on:
  schedule:
    - cron: '0 2 * * 0'  # 每周日凌晨2点

jobs:
  probe-passwords:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.8'
      - name: Install dependencies
        run: pip install paramiko
      - name: Probe vault passwords
        run: |
          python scripts/vault_password_probe.py \
            -f vault_file \
            -p "${{ secrets.VAULT_PASSWORD }}" \
            --output probe_results.json
      - name: Upload results
        uses: actions/upload-artifact@v2
        with:
          name: probe-results
          path: probe_results.json
```

## 安全注意事项

1. **密码保护**: 确保vault密码的安全，不要在命令行中明文传递
2. **网络安全**: 探测过程会进行SSH连接，确保网络环境安全
3. **备份重要**: 在生产环境中使用前，务必创建备份
4. **权限控制**: 确保运行脚本的用户有适当的文件访问权限

## 故障排除

### 常见问题

1. **连接超时**
   - 增加 `--timeout` 参数值
   - 检查网络连接和防火墙设置

2. **认证失败**
   - 验证用户名和密码是否正确
   - 检查SSH服务配置

3. **文件权限错误**
   - 确保有读写vault文件的权限
   - 检查文件路径是否正确

4. **依赖缺失**
   ```bash
   pip install paramiko
   ```

### 调试模式
```bash
# 使用详细输出进行调试
python scripts/vault_password_probe.py -f vault_file -p password --verbose --dry-run
```

## 示例

查看完整的使用示例：
```bash
# 运行演示脚本
python scripts/demo_password_probe.py
```

这个功能大大简化了vault文件的维护工作，确保连接信息的准确性和时效性。
