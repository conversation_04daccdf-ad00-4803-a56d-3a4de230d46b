# -*- coding: utf-8 -*-
# !/usr/bin/python

import xlwt

from item import Device
from utils import DEVICES_VARS


class XlsExporter:
    def __init__(self, workbook_path='Devices.xlsx'):
        self.workbook = xlwt.Workbook(encoding='utf-8')
        self.workbook_path = workbook_path

    def save_workbook(self):
        self.workbook.save(self.workbook_path)

    def write_devices(self, sheet_name, devices,
                      row_vars=DEVICES_VARS, ):
        # 存储是否写过的DeviceInstanceID
        have_write = set()
        sheet = self.workbook.add_sheet(sheet_name, cell_overwrite_ok=False)
        row_names = list(row_vars.keys())
        for i in range(len(row_names)):
            sheet.write(0, i, row_names[i])
        i = 0
        for device in devices:
            if isinstance(device, Device):
                if device.InstanceID in have_write:
                    continue
                have_write.add(device.InstanceID)
            i += 1
            for j, key in enumerate(row_names):
                sheet.write(i, j, row_vars.get(key)(device))
