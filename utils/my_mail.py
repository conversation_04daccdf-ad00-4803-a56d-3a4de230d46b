# -*- coding: utf-8 -*-
# !/usr/bin/python

import configparser
import smtplib
from email.header import Head<PERSON>
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

config = configparser.ConfigParser()

def mail_text(content):
    return MIMEText(content, 'plain', 'utf-8')

def mail_html(content):
    return MIMEText(content, 'html', 'utf-8')

def mail_file(file_path, file_type, show_name):
    att = MIMEApplication(open(file_path, 'rb').read(), _subtype=file_type, name="{}.{}".format(show_name, file_type))
    return att

def send_mail(
        receivers, subject, contents,
        mail_user,
        mail_password,
        mail_host,
        sender='<EMAIL>',
        debug_level = 0
):
    message = MIMEMultipart()
    message['From'] = Header(sender, 'utf-8')
    message['To'] = Header(','.join(receivers), 'utf-8')
    message['Subject'] = Header(subject, 'utf-8')
    if not isinstance(contents, list) or not contents:
        raise Exception('contents need to be a list! Please use "from email.mime.text import MIMEText"')
    # if not isinstance(contents[0], MIMEText):
    #     raise Exception('content need be MIMEText!')
    for content in contents:
        message.attach(content)
    
    smtpObj = smtplib.SMTP_SSL(mail_host, 465)
    smtpObj.set_debuglevel(debug_level)
    smtpObj.login(mail_user, mail_password)
    print('Send Mail')
    smtpObj.sendmail(sender, receivers, message.as_string())
    smtpObj.quit()
    print('Send Finish')