# -*- coding: utf-8 -*-
# !/usr/bin/python

import collections
import hashlib
import hmac
import json
import logging
import logging.handlers
import time

import requests
from six import u

from item import Device
from utils.HTMLTable import HTMLTable

DEVICES_VARS = collections.OrderedDict()
DEVICES_VARS['主机名'] = lambda x: x.__dict__.get('HostName', 'CMDB未查询到') if isinstance(x, Device) else x.get('hostname',
                                                                                                           'CMDB未查询到')
DEVICES_VARS['管理员A'] = lambda x: str(x.__dict__.get('ManagerA', '')) if isinstance(x, Device) else x.get('managerA',
                                                                                                         'CMDB未查询到')
DEVICES_VARS['管理员B'] = lambda x: str(x.__dict__.get('ManagerB', '')) if isinstance(x, Device) else x.get('managerB',
                                                                                                         'CMDB未查询到')
DEVICES_VARS['主机OS类型'] = lambda x: x.__dict__.get('CSOSVersion', '其他') if isinstance(x, Device) else x.get('osDistro',
                                                                                                           '其他')
DEVICES_VARS['设备在线'] = lambda x: x.__dict__.get('CIStatus', 'CMDB未查询到') if isinstance(x, Device) else x.get('status',
                                                                                                            'CMDB未查询到')
DEVICES_VARS['主机IP'] = lambda x: x.__dict__.get('IPAddress', 'CMDB未查询到') if isinstance(x, Device) else x.get('ip',
                                                                                                             'CMDB未查询到')
DEVICES_VARS['部门组别'] = lambda x: x.__dict__.get('ManagerGroup', 'CMDB未查询到') if isinstance(x, Device) else x.get(
    'userGroupManager', 'CMDB未查询到')


def devices_to_html_table(caption, devices, row_vars=DEVICES_VARS):
    """
    对Devices这个Iter的每个元素做处理
    """
    table = HTMLTable(caption=caption, value_formatter=u)
    row_names = row_vars.keys()
    table.append_header_rows([row_names, ])
    data_rows = []

    devices = sorted(devices, key=DEVICES_VARS['管理员A'])

    for device in devices:
        data_rows.append([str(row_vars.get(key)(device)) for key in row_vars.keys()])

    table.append_data_rows(data_rows)
    table.caption.set_style({'font-size': '15px'})
    table.set_style({
        'border-collapse': 'collapse',
        'word-break': 'keep-all',
        'white-space': 'nowrap',
        'font-size': '14px'
    })
    table.set_header_row_style({'color': '#fff', 'background-color': '#48a6fb', 'font-size': '18px'})
    table.set_header_cell_style({'padding': '15px'})
    return table


def get_logger(name, logfile_path, s_level=logging.WARNING, f_level=logging.ERROR):
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    formatter = logging.Formatter("%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s")
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(s_level)
    stream_handler.setFormatter(formatter)

    rf_handler = logging.handlers.TimedRotatingFileHandler(
        logfile_path, when='midnight', interval=1, backupCount=7,
        # atTime=datetime.time(0, 0, 0, 0)
    )
    rf_handler.setFormatter(formatter)

    f_handler = logging.FileHandler(logfile_path + ".error.log", mode='a')
    f_handler.setLevel(f_level)
    f_handler.setFormatter(formatter)

    logger.addHandler(stream_handler)
    logger.addHandler(rf_handler)
    logger.addHandler(f_handler)
    return logger


def _decode_list(data):
    rv = []
    for item in data:
        if isinstance(item, unicode):
            item = item.encode('utf8')
        elif isinstance(item, list):
            item = _decode_list(item)
        elif isinstance(item, dict):
            item = _decode_dict(item)
        rv.append(item)
    return rv


def _decode_dict(data):
    rv = {}
    for k, v in data.items():
        if isinstance(k, unicode):
            k = k.encode('utf8')
        if isinstance(v, unicode):
            v = v.encode('utf8')
        elif isinstance(v, list):
            v = _decode_list(v)
        elif isinstance(v, dict):
            v = _decode_dict(v)
        rv[k] = v
    return rv


def my_print(element):
    if isinstance(element, dict):
        print(json.dumps(element, indent=2, ensure_ascii=False))
    elif isinstance(element, list):
        for item in element:
            my_print(item)
    else:
        print(element)


class CMDBApi:
    def __init__(
            self,
            access_key,
            secret_key,
            host
    ):
        self.ACCESS_KEY = access_key
        self.SECRET_KEY = secret_key
        self.HOST = host
        self.HEADER = {'host': 'openapi.easyops-only.com', 'Content-Type': 'application/json'}

    # 生成签名算法
    def signature(self, request_time, method, url, data):
        # 签名准备
        method = method.upper()
        if method in ["GET", "DELETE"]:
            params = "".join(["%s%s" % (key, data[key]) for key in sorted(data.keys())])
            content = ''
        else:
            params = ''
            m = hashlib.md5()
            m.update(json.dumps(data).encode('utf-8'))
            content = m.hexdigest()
        str_sign = '\n'.join([
            method,
            url,
            params,
            'application/json',
            content,
            str(request_time),
            self.ACCESS_KEY])
        # 生成签名
        return hmac.new(self.SECRET_KEY, str_sign, hashlib.sha1).hexdigest()

    # 发起请求函数
    def do_request(self, host, op_url, method, header, data):
        url = "https://%s%s" % (host, op_url)
        print(u"准备发送请求: %s \n %s \n %s \n %s" % (method, url, header, str(data)))
        try:
            # 发起请求
            if method.upper() in ["GET", "DELETE"]:
                r = requests.request(method=method, url=url, headers=header, params=data, verify=False)
            else:
                r = requests.request(method=method, url=url, headers=header, json=data, verify=False)
            # print r.text
            # 请求状态判断
            if r.status_code == 200:
                print(u"请求成功!")
                jsn = r.json()
                return jsn
            else:
                print(u"请求失败!错误码 %s 详情为:%s" % (r.status_code, r.text))
        except Exception as e:
            print(e)

    # 发起OpenApi请求函数
    def start(self, method, op_url, data=None):
        # 签名预备
        data_ = data or {}
        time_ = int(time.time())
        # 生成签名
        signature = self.signature(request_time=time_, method=method, url=op_url, data=data_)
        # 添加keys并发起连接
        op_url = "%s?accesskey=%s&signature=%s&expires=%s" % (op_url, self.ACCESS_KEY, signature, str(time_))
        return self.do_request(header=self.HEADER, host=self.HOST, op_url=op_url, method=method, data=data)

    def get_all_data(self, query=None, fields=None, object_name="HOST"):
        if query is None:
            query = {"status": {"$eq": "运行中"}, }
        if fields is None:
            fields = {
                "ip": True,
                "hostname": True,
                "status": True,
                "sn": True,
                "model": True,
                "item": True,
                "deviceVersion": True,
                "layer": True,
                "userGroupManager": True,
                "managerA": True,
                "managerB": True,
                "manageIP": True,
                "outBandIP": True,
                "defaultIP": True,
                "businessIP": True,
                "osDistro": True,
                "physicalServer.outBandIP": True,
                "base_physical_server": True,
                "_openstackserver._host._host__IDCRACK._rack__IDC": True,
                "vmware_vm.VMWARE_HOST_COMPUTER._idc": True,
                "physicalServer.rack.name": True,
                "APPSZSE.nameN": True,
                "APPSZSE.name": True,
                "APPSZSE.equalPLevel": True,
                "APPSZSE.securityLevel": True,
                "APPSZSE.impLevel": True,
            }
        page = 0
        reach = False
        res = []
        while not reach:
            page += 1
            app_params = {
                "query": query,
                "fields": fields,
                "page": page,
                "page_size": 3000
            }
            appDatas = self.start(
                method='POST',
                op_url='/cmdb_resource/object/{}/instance/_search'.format(object_name), data=app_params
            )
            res.extend(appDatas['data']['list'])
            total = appDatas['data']['total']
            if total <= 3000 * page:
                reach = True
        return res

    # https://cmdb.sse.org.cn/next/cmdb-model-management/object/HOST/detail
    def get_host_data(self, query=None, fields=None):
        return self.get_all_data(query, fields, object_name='HOST')

    # https://cmdb.sse.org.cn/next/cmdb-model-management/object/PHYSICAL_SERVER@ONEMODEL/detail
    def get_physical_server_data(self, query=None, fields=None):
        if fields is None:
            fields = {
                'name': True,
                'status': True,
                'sn': True,
                'manageIP': True,
                'brand': True,
                'mdl': True,
                'sysVersion': True,
                'storehouse': True,
                'managerA': True,
                'managerB': True,
                'cpuModel': True,
                'memorySize': True,
                'diskSize': True,
                'diskInfo': True,
                'diskCount': True
                # 'assetsSn': True,
                # 'maintenanceStartTime': True,
                # 'outboundTime': True,
                # 'purchaseDate': True,
                # 'rackMountingTime': True,
                # 'rackUnmountingTime': True
            }
        return self.get_all_data(query, fields, object_name='PHYSICAL_SERVER@ONEMODEL/')
