# -*- coding: utf-8 -*-
# !/usr/bin/python

"""
演示vault密码探测功能
"""

import sys
import json
import getpass
import os
sys.path.append('../')

from my_ansible import MyAnsibleVaultManager, probe_vault_passwords


def create_demo_vault():
    """
    创建演示用的vault文件
    """
    demo_vault_data = {
        "*************": {
            "connections": [
                {"user": "root", "password": "valid_password"},
                {"user": "admin", "password": "invalid_password"}
            ]
        },
        "*************": {
            "connections": [
                {"user": "root", "password": "another_invalid_password"}
            ]
        },
        "localhost": {
            "connections": [
                {"user": "testuser", "password": "testpass"},
                {"user": os.getenv('USER', 'user'), "password": "wrongpass"}
            ]
        }
    }
    
    return demo_vault_data


def demo_password_probe():
    """
    演示密码探测功能
    """
    print("演示：Vault密码探测功能")
    print("="*50)
    
    # 创建临时vault文件用于演示
    demo_vault_path = "demo_vault_file"
    vault_password = "demo_password"
    
    try:
        # 1. 创建演示vault数据
        print("1. 创建演示vault数据...")
        vault_manager = MyAnsibleVaultManager(demo_vault_path, vault_password)
        vault_manager.vault_vars = create_demo_vault()
        
        # 显示原始数据
        print("原始vault数据:")
        for host_ip, host_data in vault_manager.vault_vars.items():
            print(f"  {host_ip}:")
            for conn in host_data.get('connections', []):
                print(f"    - {conn['user']}: {conn['password']}")
        
        # 加密保存
        vault_manager.encrypt_file()
        print(f"✓ 已创建演示vault文件: {demo_vault_path}")
        
        # 2. 获取vault统计信息
        print("\n2. Vault统计信息:")
        stats = vault_manager.get_vault_statistics()
        print(f"  总主机数: {stats['total_hosts']}")
        print(f"  总连接数: {stats['total_connections']}")
        print(f"  用户列表: {stats['users']}")
        
        # 3. 执行密码探测
        print("\n3. 开始密码探测...")
        results = vault_manager.probe_vault_passwords(
            timeout=3,
            max_workers=5,
            save_changes=True
        )
        
        # 4. 显示探测后的数据
        print("\n4. 探测后的vault数据:")
        vault_manager.decrypt_file()  # 重新读取更新后的数据
        
        if vault_manager.vault_vars:
            for host_ip, host_data in vault_manager.vault_vars.items():
                print(f"  {host_ip}:")
                connections = host_data.get('connections', [])
                if connections:
                    for conn in connections:
                        print(f"    - {conn['user']}: {conn['password']}")
                else:
                    print("    (无有效连接)")
        else:
            print("  (所有连接都已被删除)")
        
        return results
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        # 清理演示文件
        if os.path.exists(demo_vault_path):
            os.remove(demo_vault_path)
            print(f"\n✓ 已清理演示文件: {demo_vault_path}")


def demo_with_real_vault():
    """
    使用真实vault文件进行演示
    """
    print("\n演示：使用真实vault文件")
    print("="*50)
    
    vault_file = input("请输入vault文件路径 (回车跳过): ").strip()
    if not vault_file:
        print("跳过真实vault文件演示")
        return
    
    if not os.path.exists(vault_file):
        print(f"文件不存在: {vault_file}")
        return
    
    vault_password = getpass.getpass("请输入vault密码: ")
    
    try:
        print(f"正在处理vault文件: {vault_file}")
        
        # 使用便捷函数
        results = probe_vault_passwords(
            vault_file_path=vault_file,
            vault_password=vault_password,
            timeout=5,
            max_workers=10,
            save_changes=False  # 演示模式不保存更改
        )
        
        if 'error' in results:
            print(f"处理失败: {results['error']}")
        else:
            print("处理完成！")
            
            # 询问是否保存更改
            if results['removed_connections']:
                save = input("是否保存更改到vault文件? (y/N): ").strip().lower()
                if save == 'y':
                    # 重新运行并保存更改
                    probe_vault_passwords(
                        vault_file_path=vault_file,
                        vault_password=vault_password,
                        timeout=5,
                        max_workers=10,
                        save_changes=True
                    )
                    print("✓ 已保存更改")
                else:
                    print("未保存更改")
        
    except Exception as e:
        print(f"处理真实vault文件时出错: {e}")


def analyze_vault_file(vault_file_path, vault_password):
    """
    分析vault文件内容
    
    Args:
        vault_file_path: vault文件路径
        vault_password: vault密码
    """
    try:
        vault_manager = MyAnsibleVaultManager(vault_file_path, vault_password)
        vault_manager.decrypt_file()
        
        if not vault_manager.vault_vars:
            print("Vault文件为空或解密失败")
            return
        
        print("\nVault文件分析:")
        print("-" * 30)
        
        stats = vault_manager.get_vault_statistics()
        
        print(f"总主机数: {stats['total_hosts']}")
        print(f"有连接的主机数: {stats['hosts_with_connections']}")
        print(f"总连接数: {stats['total_connections']}")
        print(f"用户数: {len(stats['users'])}")
        
        print(f"\n用户分布:")
        for user, hosts in stats['hosts_by_user'].items():
            print(f"  {user}: {len(hosts)} 台主机")
        
        # 检查重复密码
        password_usage = {}
        for host_ip, host_data in vault_manager.vault_vars.items():
            if isinstance(host_data, dict) and 'connections' in host_data:
                for conn in host_data['connections']:
                    password = conn.get('password', '')
                    if password not in password_usage:
                        password_usage[password] = []
                    password_usage[password].append(f"{host_ip}({conn.get('user', 'unknown')})")
        
        duplicate_passwords = {pwd: hosts for pwd, hosts in password_usage.items() if len(hosts) > 1}
        if duplicate_passwords:
            print(f"\n重复使用的密码:")
            for password, hosts in duplicate_passwords.items():
                print(f"  密码 '{password[:8]}...': {len(hosts)} 次使用")
                for host in hosts[:3]:  # 只显示前3个
                    print(f"    - {host}")
                if len(hosts) > 3:
                    print(f"    ... 还有 {len(hosts)-3} 个")
        
    except Exception as e:
        print(f"分析vault文件时出错: {e}")


if __name__ == '__main__':
    print("Vault密码探测功能演示")
    print("="*50)
    
    # 演示基本功能
    demo_results = demo_password_probe()
    
    # 演示真实vault文件处理
    demo_with_real_vault()
    
    print("\n功能特性:")
    print("1. 并发测试多个SSH连接")
    print("2. 自动删除无效的连接信息")
    print("3. 清理空的主机条目")
    print("4. 提供详细的探测报告")
    print("5. 支持自定义超时和并发数")
    print("6. 可选择是否自动保存更改")
    print("7. 提供vault文件统计分析")
    
    print("\n使用方法:")
    print("from my_ansible import probe_vault_passwords")
    print("results = probe_vault_passwords('vault_file', 'password')")
