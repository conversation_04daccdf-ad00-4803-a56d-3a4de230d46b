# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append('../')

import requests
import xlrd
import getpass
import os
from utils import CMDBApi
from utils import my_print, get_logger
from resources.config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host
import subprocess
from my_ansible import *

logger = get_logger(__name__, 'ris.log')

cmdb_api = CMDBApi(
    access_key=cmdb_api_access_key,
    secret_key=cmdb_api_secret_key,
    host=cmdb_api_host
)

LOCAL_DEPARTMENT = {
    "信息科技三部": 1000,
    "服务器及存储基础设施组": 1012,
    "信息安全组": 1021,
    "运维需求与技术组": 1020,
    "应用管理组": 1014,
    "大数据平台组": 1016,
}

LOCAL_USER_GROUP = {
    "服务器及存储基础设施组": 3,
    "应用管理组": 18,
    "大数据平台组": 9
}

LOCAL_USERS = {
    'chenlei': 462
}


def is_reachable(ris_data):
    ip = ris_data.get('ip')
    with open(os.devnull, 'w') as devnull:
        retcode = subprocess.call(args=['ping', '-c', '1', '-W', '1', ip], stdout=devnull, stderr=devnull)
    return (ris_data, retcode == 0)


class QiZhiHelper:
    """
    API 对接，详见
    https://pam.sse.org.cn/shterm/resources/docs/rest/index.html
    使用create_dev_json等来构造参数
    """

    def __init__(self):
        user = 'sysadmin'
        password = getpass.getpass('请输入“密码 动态令牌”：')
        r = requests.post(
            url='https://pam.sse.org.cn/shterm/api/authenticate',
            params={'username': user, 'password': password}
        )
        if r.status_code != 200:
            exit(1)
        self.token = r.json().get('ST_AUTH_TOKEN')
        self.department = self.get_all_department()

    def get_all_dev(self):
        page = 0
        reach = False
        all_dev_data = []
        while not reach:
            response = requests.get(
                url='https://pam.sse.org.cn/shterm/api/dev?deleteIs=false&page={}'.format(page),
                headers={'Content-Type': 'Happlication/json', 'st-auth-token': self.token},
            ).json()
            if response['last']:
                reach = True
            page += 1
            all_dev_data.extend(response['content'])
        return all_dev_data

    def search_dev(self, name):
        response = requests.get(
            url='https://pam.sse.org.cn/shterm/api/dev?deleteIs=false&name={}'.format(name),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
        ).json()
        if response.get('last'):
            dev_id = response['content'][0]['id']
            return dev_id
        return None

    def add_dev(self, dev_dict):
        response = requests.post(
            url='https://pam.sse.org.cn/shterm/api/dev',
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            json=dev_dict
        )
        return response

    def delete_dev(self, dev_id):
        response = requests.delete(
            url='https://pam.sse.org.cn/shterm/api/dev/{}'.format(dev_id),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
        )
        if response.status_code == 204:
            my_print(response)

    def delete_dev_by_name(self, name):
        """
        删除资产，可支持IP，但IP可能存在重复使用的现象，最好用name
        """
        response = requests.post(
            url='https://pam.sse.org.cn/shterm/api/dev/deleteByIpOrName',
            headers={'Content-Type': 'Happlication/json', 'st-auth-token': self.token},
            json={"server": name}
        ).json()
        if response.status_code == 204:
            my_print(response)

    def edit_dev(self, dev_id, dev_dict):
        """
        {
            "ip": "*******",
            "sysType": {
                "id": 3
            },
            "extInfo": {
                "1": "shterm"
            },
            "state": 0,
            "charset": "ISO-8859-1",
            "description": "shterm is very good",
            "services": {
                "services": {
                    "ssh": {
                        "port": 5520,
                        "proto": "ssh",
                        "state": 0
                    }
                }
            },
            "accounts": {
                "accounts": [
                    {
                        "name": "shterm",
                        "password": "shterm",
                        "priv": false
                    }
                ]
            }
        }
        """
        response = requests.put(
            url='https://pam.sse.org.cn/shterm/api/dev/{}'.format(dev_id),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            data=dev_dict
        )
        return response

    def search_user(self, name):
        response = requests.get(
            url='https://pam.sse.org.cn/shterm/api/user?loginName={}'.format(name),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
        )
        return response

    def get_all_user_groups(self):
        response = requests.get(
            url='https://pam.sse.org.cn/shterm/api/usergroup',
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
        )
        return response

    def get_all_department(self):
        response = requests.get(
            url='https://pam.sse.org.cn/shterm/api/department',
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
        )
        return response.json()

    @staticmethod
    def create_dev_dict(
            dev_name, ip, description="autogen;",
            owner=None, sys_type_id=1, department=1000, dev_type=0
    ):
        """
        name: 资产名。
        ip: 资产IP,填写IP地址或者域名。
        dev_type：资产类别，可选值：0 - 主机，1 - 网络资产
        sys_type_id: 资产类型ID,预定义的资产类型id：主机有Linux,Windows,IBM AIX, HP UX, IBM AS/400的ID分别为"1"，"2"，"4"，"3"，"18"。
        网络资产有Cisco IOS, Huawei Quidway, Juniper NetScreen, H3C Comware,General Network的ID分别为"5"，"6"，"7"，"8"，"9"。

        """
        res = {
            "name": dev_name,
            "ip": ip,
            "type": dev_type,
            "sysType": {
                "id": sys_type_id
            },
            "department": {
                "id": department
            },
            "charset": "UTF-8",
        }
        if owner:
            res['owner'] = owner
        if description:
            res["description"] = description
        return res

    @staticmethod
    def trans_cmdb_device_data_to_ris_dev_dict(device_data):
        sys_type_id = 1
        if 'windows' in device_data.get("osDistro", 'default_os'):
            sys_type_id = 2
        res = QiZhiHelper.create_dev_dict(
            dev_name=device_data.get("hostname", 'autogen_default'),
            ip=device_data.get("manageIP", '*******'),
            sys_type_id=sys_type_id
        )
        return res

    @staticmethod
    def create_dynamic_rule_dict(name, users, user_groups, devs, protocols=None, accounts=None, department=1000):
        """
        name: autogen_<app_name>_rule
        users: list of user id
        devs: list of dev id
        department: department id
        """
        proto_type = 0
        if not protocols:
            protocols = ["ssh"]
            proto_type = 1
        if not accounts:
            accounts = ["any", "self", "root"]
        res = {
            'name': name,
            # 指定用户类型。0-全部用户 1-用户/用户组 2-指定规则，不传将使用默认值0。
            'userType': 1,
            # 指定资产类型。0-全部资产 1-资产/资产组 2-指定规则，不传将使用默认值0。
            'resType': 1,
            # 指定协议类型。0-全部协议 1-指定协议，不传将使用默认值0。
            'protoType': proto_type,
            # 指定账号类型。0-全部账号 1-指定账号 2-指定规则，不传将使用默认值0。 注意：0-全部账号时，包含self和any账号.
            'accountType': 1,
            # 规则模板。
            'loginRuleTemplate': {'id': 1},
            'configurations': {
                "resConfs": {"devs": devs},
                "userConfs": {"users": users, "userGroups": user_groups},
                "protocols": protocols,
                "accountConfs": {"accounts": accounts}
            },
            "department": {
                "id": department,
            }
        }
        return res

    def search_dynamic_rule(self):
        pass

    def create_dynamic_rule_by_name(self, rule_dict):
        """
        请求示例2： 指定用户 + 指定资产 + 指定账号 + 指定协议：
        {
            "name":"dr2",
            "loginRuleTemplate":{
                "id":1
            },
            "userType":1,
            "resType":1,
            "accountType":1,
            "protoType":1,
            "configurations":{
                "userConfs":{
                    "users":[
                         {"id":1},
                         {"id":2},
                         {"name":"admin"},
                         {"name":"root"},
                         {"id":1, "name":"admin"},
                         {"id":2, "name":"root"}
                    ],
                    "userGroups":[
                         {"id":1},
                         {"id":2},
                         {"name":"用户组1"},
                         {"name":"用户组2"},
                         {"id":1, "name":"用户组1"},
                         {"id":2, "name":"用户组2"}
                    ]
                },
                "resConfs":{
                    "devs":[
                         {"id":1},
                         {"id":2},
                         {"name":"资产1"},
                         {"name":"资产2"},
                         {"id":1, "name":"资产1"},
                         {"id":2, "name":"资产2"}
                    ],
                    "devGroups":[
                         {"id":1},
                         {"id":2},
                         {"name":"资产组1"},
                         {"name":"资产组2"},
                         {"id":1, "name":"资产组1"},
                         {"id":2, "name":"资产组2"}
                    ]
                },
                "accountConfs":{
                    "accounts":[
                         {"name":"root"},
                         {"name":"root1"}
                    ]
                },
                "protocols":[
                     {"name":"ssh"},
                     {"name":"rdp"}
                ]
            },
            "department":{"id":1}
        }
        """
        response = requests.post(
            url='https://pam.sse.org.cn/shterm/api/dynamicRule/createByName',
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            json=rule_dict,
            verify=False
        )
        return response

    def get_all_dynamic_rules(self):
        page = 0
        reach = False
        all_ris_data = []
        while not reach:
            response = requests.get(
                url='https://pam.sse.org.cn/shterm/api/dynamicRule/getList?page={}'.format(page),
                headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            ).json()
            if response['last']:
                reach = True
            page += 1
            all_ris_data.extend(response['content'])
        return all_ris_data

    def edit_dynamic_rule(self, rule_id, dynamic_rule_json):
        response = requests.put(
            url='https://pam.sse.org.cn/shterm/api/dynamicRule/{}'.format(rule_id),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            json=dynamic_rule_json,
            verify=False
        )
        return response

    def edit_dynamic_rule_by_name(self, old_name, rule_dict):
        """
        该API支持以name为关键词
        """
        rule_dict["old_name"] = old_name
        response = requests.put(
            url='https://pam.sse.org.cn/shterm/api/dynamicRule/updateByName',
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            json=rule_dict,
            verify=False
        )
        return response

    def delete_dynamic_rule(self):
        pass

    def add_dev_to_res_group(self, group_id, devs):
        response = requests.put(
            url='https://pam.sse.org.cn/shterm/api/resGroup/relateDevs/{}'.format(group_id),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            json=[{"id": a} for a in devs]
        )
        return response

    def remove_dev_from_res_group(self, group_id, devs):
        response = requests.put(
            url='https://pam.sse.org.cn/shterm/api/resGroup/removeDevs/{}'.format(group_id),
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
            json=[{"id": a} for a in devs]
        )
        return response

    def get_all_dev_type(self):
        response = requests.get(
            url='https://pam.sse.org.cn/shterm/api/devType',
            headers={'Content-Type': 'application/json;charset=utf-8', 'st-auth-token': self.token},
        )
        return response.json()


class QiZhiPasswordHelper(MyAnsibleVaultManager):
    def __init__(self, qizhi_filepath, vault_file, vault_password):
        super(QiZhiPasswordHelper, self).__init__(vault_file, vault_password)
        self.qizhi_filepath = qizhi_filepath
        self.qizhi_vars = {}
        self._qizhi_users = set()
        self._load_file()

    def _load_file(self):
        xl = xlrd.open_workbook(self.qizhi_filepath)
        table = xl.sheets()[0]
        for i in range(1, table.nrows):
            row = table.row_values(i)
            hostname = row[0]
            ip = row[1]
            dev_type = row[2]
            user = row[4]
            change = row[6]
            password = row[7]
            if dev_type != 'Linux':
                continue
            self._qizhi_users.add(user)
            if ip not in self.qizhi_vars:
                self.qizhi_vars[ip] = {"connections": [{"password": password, "user": user}]}
            else:
                self.qizhi_vars[ip]["connections"].append({"password": password, "user": user})

    def test_connection(self):
        self.decrypt_file()
        ips = self.qizhi_vars.keys()
        mas = MyAnsibleService(
            timeout=1, forks=16,
            inventory=','.join(ips),
        )
        mas.variable_manager.extra_vars.update(self.qizhi_vars)

        for user in self._qizhi_users:
            mas.run(
                play_source=DEFAULT_VAULT_PLAY_SOURCE,
                extra_vars={'default_user': user}
            )
            unreachable_ips = mas.results_callback.host.get('unreachable', {}).keys()
            ok_ips = list(
                set(mas.results_callback.host.get('ok', {}).keys()) - set(unreachable_ips)
            )
            for ip in unreachable_ips:
                if ip not in self.vault_vars:
                    continue
                for connection in self.qizhi_vars[ip]['connections']:
                    if connection['user'] == user:
                        cur_password = connection['password']

                new_connections = list(filter(
                    lambda x: not (x['user'] == user and x['password'] == cur_password),
                    mas.vault_manager.vault_vars[ip]['connections']
                ))
                self.vault_vars[ip]['connections'] = new_connections

            print('Update ip\n')
            for ip in ok_ips:
                print(ip)
            print('update {} ips\n'.format(len(ok_ips)))
            for ip in ok_ips:
                cur_password = None
                for connection in self.qizhi_vars[ip]['connections']:
                    if connection['user'] == user:
                        cur_password = connection['password']
                if not cur_password:
                    continue
                if ip not in self.vault_vars:
                    self.vault_vars[ip] = {"connections": [{'user': user, 'password': cur_password}]}
                else:
                    add_new = True
                    for connection in self.vault_vars[ip]['connections']:
                        if connection['user'] == user:
                            add_new = False
                            connection['password'] = cur_password
                    if add_new:
                        self.vault_vars[ip]['connections'].append({'user': user, 'password': cur_password})
        self.encrypt_file()


QIZHI_HELPER = QiZhiHelper()
all_ris_data = QIZHI_HELPER.get_all_dev()

all_device_data = cmdb_api.get_host_data(
    query={
        "USER_GROUP_MANAGE.name": {
            "$nin": ['交易主机组', '交易运行组']
        }
    },
    fields={
        "ip": True,
        "hostname": True,
        "status": True,
        "USER_GROUP_MANAGE": True,
        "USER_MANAGE_A": True,
        "USER_MANAGE_B": True,
        "manageIP": True,
        "businessIP": True,
        "osDistro": True,
        "APPSZSE": True,
    }
)


def update_ris_password(var_file='/home/<USER>/resources/baseline_vault'):
    # 只监控有IP的，没有IP的是应用发布
    all_dev_data = [a for a in all_ris_data if 'ip' in a]
    vault_file_password = getpass.getpass('Input Password: ')
    vault_manager = MyAnsibleVaultManager(
        vault_file=var_file,
        vault_password=vault_file_password
    )
    vault_manager.decrypt_file()
    ip_dev = {}
    for dev_data in all_dev_data:
        # 因该不会有，但是还是有重复的数据，需要查明原因
        if dev_data['ip'] in ip_dev:
            print('found duplicate ip!')
            my_print(ip_dev[dev_data['ip']])
            continue
        ip_dev[dev_data['ip']] = dev_data
    ip_device = {
        device.get('manageIP', device.get('ip')): device for device in all_device_data
    }
    id_dev_vars = {}

    # 这里只会已经保存密码的
    for ip, connections in vault_manager.vault_vars.items():
        dev_dict = ip_dev.get(ip)
        device_dict = ip_device.get(ip)
        if not dev_dict or not device_dict:
            continue
        dev_id = dev_dict['id']
        dev_ip = dev_dict['ip']
        dev_name = dev_dict['name']
        if device_dict['hostname']:
            dev_name = device_dict['hostname']
        account_conf = []
        for connection in connections['connections']:
            connection_user = connection['user']
            connection_password = connection['password']
            account_conf.append(
                {
                    "name": connection_user,
                    "password": connection_password,
                    "priv": 'root' == connection_user,
                }
            )
        id_dev_vars[dev_id] = {
            "name": dev_name,
            "ip": dev_ip,
            "charset": "UTF-8",
            "state": 0,
            "accounts": {
                "accounts": account_conf
            },
            # type0 主机
            "type": 0,
            "department": {
                "id": 1000
            },
            # devType Linux主机
            "sysType": {
                "id": 1
            },
            "description": "autogen;"
        }

    # ! 编辑机器来修改密码
    for dev_id, var_dev_dict in id_dev_vars.items():
        QIZHI_HELPER.edit_dev(dev_id, var_dev_dict)


def main():
    # 只监控有IP的，没有IP的是应用发布
    all_dev_data = [a for a in all_ris_data if 'ip' in a]
    all_ris_ip = [a['ip'] for a in all_ris_data if 'ip' in a]
    for ris_data in all_ris_data:
        if 'ip' not in ris_data:
            my_print(ris_data)

    # del_names = []
    # del_ids = []
    # import time
    # for dev_data in all_dev_data:
    #     dev_join_time = time.localtime(dev_data['joinTime'] // 1000)
    #     if dev_join_time.tm_year == 2025 and dev_join_time.tm_mon == 4 and dev_join_time.tm_mday == 12:
    #         if dev_data['description'] == 'autogen;':
    #             del_names.append(dev_data['name'])
    #             del_ids.append(dev_data['id'])
    #
    # if del_names:
    #     for name in del_names:
    #         try:
    #             dev_id = qiZhiHelper.search_dev(name)
    #             qiZhiHelper.delete_dev(dev_id)
    #         except Exception as e:
    #             print('delete {} name failed! Exception: {}'.format(name, e.message))
    #             continue

    all_device_ip = [a['ip'] for a in all_device_data]

    device_in_ris = []
    device_not_in_ris = []

    for a in all_device_data:
        if a['ip'] in all_ris_ip:
            device_in_ris.append(a)
        else:
            device_not_in_ris.append(a)

    device_not_in_ris = device_not_in_ris[:10]
    # ! 创建资产
    for device_data in device_not_in_ris:
        QIZHI_HELPER.add_dev(
            QIZHI_HELPER.trans_cmdb_device_data_to_ris_dev_dict(device_data)
        )

    # 修改密码的相关逻辑
    update_ris_password()

    # create dyn rules
    for device_data in all_device_data:
        host_ip = device_data.get("manageIP")
        hostname = device_data.get("hostname")
        app_info = device_data.get("APPSZSE")
        for app in app_info:
            user_group = app.get("userGroupManager")

    ## 杂项，这里ping并整理
    # device_ris_not_in_cmdb = [a for a in all_ris_data if a.get('ip') in set(all_ris_ip) - set(all_device_ip)]
    #
    # pool_size = 50
    # pool = ThreadPool(pool_size)
    # results = pool.map(is_reachable, device_ris_not_in_cmdb)
    # pool.close()
    # pool.join()
    #
    # not_ping = []
    # for ris_data, reachable in results:
    #     if not reachable:
    #         not_ping.append(ris_data)
    #
    # import collections
    # DEVICES_VARS = collections.OrderedDict()
    # DEVICES_VARS['主机名'] = lambda x: x.get('hostname', 'CMDB未查询到')
    # DEVICES_VARS['管理员A'] = lambda x: x.get('USER_MANAGE_A', [dict()])[0].get("nickname", 'CMDB未查询到')
    # DEVICES_VARS['管理员B'] = lambda x: x.get('USER_MANAGE_B', [dict()])[0].get("nickname", 'CMDB未查询到')
    # DEVICES_VARS['主机OS类型'] = lambda x: x.get('osDistro', 'CMDB未查询到')
    # DEVICES_VARS['设备在线'] = lambda x: x.get('status', 'CMDB未查询到')
    # DEVICES_VARS['主机管理IP'] = lambda x: x.get('manageIP', 'CMDB未查询到')
    # DEVICES_VARS['部门组别'] = lambda x: x.get('USER_GROUP_MANAGE', [dict()])[0].get("name", 'CMDB未查询到')
    # xlsx_path = '/tmp/tmp.xlsx'
    # xe = XlsExporter(xlsx_path)
    # xe.write_devices(sheet_name='堡垒机机器', devices=device_in_ris, row_vars=DEVICES_VARS, )
    # xe.write_devices(sheet_name='堡垒机不存在的机器', devices=device_not_in_ris, row_vars=DEVICES_VARS, )
    # for context in [('堡垒机需要清理的机器', device_ris_not_in_cmdb), ('ping不通', not_ping)]:
    #     sheet_name = context[0]
    #     device_list = context[1]
    #     sheet = xe.workbook.add_sheet(sheet_name, cell_overwrite_ok=False)
    #     row_names = list(['name', '描述', 'ip', '部门', '当前状态', '已删除'])
    #     for i in range(len(row_names)):
    #         sheet.write(0, i, row_names[i])
    #     i = 0
    #     for device in device_list:
    #         i += 1
    #         sheet.write(i, 0, device.get('name'))
    #         sheet.write(i, 1, device.get('description'))
    #         sheet.write(i, 2, device.get('ip'))
    #         sheet.write(i, 3, device.get('department', {}).get('name'))
    #         # sheet.write(i, 4, device.get('APPSZSE', [{}], )[0].get('nameN'))
    #         sheet.write(i, 4, "活动" if device.get('state') == 0 else "禁用")
    #         sheet.write(i, 5, "" if not device.get('deleted') else "已删除")
    #
    # xe.save_workbook()
    #
    # from resources.config import mail_user, mail_password, mail_host
    #
    # message = [
    #     mail_html('当前堡垒机共纳管基础组{}个IP，目前CMDB共有{}个IP，还有{}个IP需要进一步处理，'.format(
    #         len(device_in_ris), len(all_device_ip), len(all_device_ip) - len(device_in_ris))),
    #     mail_file(xlsx_path, 'xlsx', '导出机器')
    # ]
    #
    # send_mail(
    #     ['<EMAIL>', ], '堡垒机机器', contents=message,
    #     mail_user=mail_user, mail_password=mail_password, mail_host=mail_host
    # )


def update_vault_password():
    qizhipasswordhelper = QiZhiPasswordHelper(
        vault_file='/home/<USER>/resources/baseline_vault',
        vault_password=getpass.getpass('Input vault password: \n'),
        qizhi_filepath='export_6518609127193263857.xlsx'
    )
    qizhipasswordhelper.test_connection()


if __name__ == '__main__':
    main()
