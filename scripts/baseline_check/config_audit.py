#!/usr/bin/python
# coding=utf-8
import json, sys, argparse, platform
from collections import OrderedDict

reload(sys)
sys.setdefaultencoding('utf-8')

KYLIN10_BASELINE_PATH = './baseline_check_files/config/kylin_v10.json'
RHEL6_BASELINE_PATH = './baseline_check_files/config/redhat_6.json'
RHEL7_BASELINE_PATH = './baseline_check_files/config/redhat_7.json'
GENERAL_BASELINE_PATH = './baseline_check_files/config/general.json'

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='baseline check script')
    parser.add_argument('-o', '--os', type=str, default='7', help='6:redhat6, 7:redhat7, 10:kylin_v10')
    parser.add_argument('-k', '--key', type=str, default='', help='event keyword')
    parser.add_argument('-a', '--arg', action='store_true', default=False, help='list args')
    parser.add_argument('-c', '--config', action='store_true', default=False, help='list configs')
    parser.add_argument('-e', '--event', action='store_true', default=False, help='list events')
    parser.add_argument('-s', '--stat', action='store_true', default=False, help='list statistics')
    args = parser.parse_args()

    if args.os == '6':
        config_path = RHEL6_BASELINE_PATH

    if args.os == '7':
        config_path = RHEL7_BASELINE_PATH

    if args.os == '10':
        config_path = KYLIN10_BASELINE_PATH

    with open(config_path) as f:
        spec_events = json.load(f)

    with open(GENERAL_BASELINE_PATH) as f:
        general_events = json.load(f)

    all_events = spec_events + general_events
    filtered_events = []
    for event in all_events:
        if args.key in event['event']:
            filtered_events.append(event)

    event_count = 0
    config_count = 0
    arg_count = 0
    plugin_dict = {}

    if args.stat:
        for event in filtered_events:
            event_count += 1
            for config in event['configs']:
                config_count += 1
                for arg in config['args']:
                    arg_count += 1

        print('events: {}'.format(event_count))
        print('configs: {}'.format(config_count))
        print('args: {}'.format(arg_count))

    if args.event:
        for event in filtered_events:
            print(event['event'])

    if args.config:
        for event in filtered_events:
            print(event['event'])
            for config in event['configs']:
                print('  ' + config['name'])

    if args.arg:
        for event in filtered_events:
            print(event['event'])
            for config in event['configs']:
                print('  ' + config['name'])
                for arg in config['args']:
                    checker = config['checker_type']
                    args_str = ''
                    for k, v in arg.items():
                        args_str += k
                        args_str += '='
                        args_str += v
                        args_str += ', '

                    spec_str = '    [{}] {}'.format(checker, args_str)
                    print(spec_str)