<mxfile host="65bd71144e">
    <diagram id="HUIeXhjt0njhr_7AQ_oW" name="第 1 页">
        <mxGraphModel dx="1033" dy="792" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="&lt;h1&gt;基线巡检脚本工作流程&lt;/h1&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="40" y="40" width="510" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="CMDB" style="html=1;fontSize=12;align=center;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="320" y="120" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="&lt;span style=&quot;font-size: 12px;&quot;&gt;被管控机&lt;/span&gt;" style="html=1;fontSize=18;align=center;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="560" y="120" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="11">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="1080" as="sourcePoint"/>
                        <mxPoint x="50" y="210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="4">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="600" y="1080" as="sourcePoint"/>
                        <mxPoint x="599.5" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="360" y="240" as="sourcePoint"/>
                        <mxPoint x="120" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="1.从CMDB主机模型获取基础设施组Linux主机信息&lt;br&gt;（get_jcssz_devices）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="170" y="210" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="管控机&lt;br&gt;python进程" style="html=1;fontSize=12;align=center;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="80" y="120" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="360" y="1080" as="sourcePoint"/>
                        <mxPoint x="530" y="370" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="119.5" y="360" as="sourcePoint"/>
                        <mxPoint x="119.5" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="2.根据脚本入参生成baseline_playbook_runtime.yml&lt;br&gt;(gen_runtime_playbook)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="130" y="290" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="119.5" y="480" as="sourcePoint"/>
                        <mxPoint x="119.5" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="3.根据脚本入参生成baseline_playbook_runtime.yml&lt;br&gt;(gen_runtime_playbook)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="130" y="410" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="4.通过playbook依次对各管理员名下主机执行命令" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="270" y="520" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="559" as="sourcePoint"/>
                        <mxPoint x="600" y="559" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="4.1 删除/tmp目录下基线巡检文件&lt;br&gt;（rm /tmp/baseline_check_* -rf）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="610" y="620" width="210" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="599.5" y="680" as="sourcePoint"/>
                        <mxPoint x="599.5" y="600" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="720" as="sourcePoint"/>
                        <mxPoint x="600" y="720" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="4.2 将基线巡检脚本传输至被管控机 /tmp/baseline_check_files.tar.gz" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="270" y="680" width="190" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="599.5" y="840" as="sourcePoint"/>
                        <mxPoint x="599.5" y="760" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="4.3 运行基线巡检脚本，生成巡检结果文件&lt;br&gt;/tmp/baseline_check_files/result.json&lt;br&gt;（sudo python ./baseline_check.py）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="610" y="780" width="260" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="600" y="880" as="sourcePoint"/>
                        <mxPoint x="120" y="880" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="4.4 将巡检结果文件传输至管控机&amp;nbsp;&lt;br&gt;/tmp/raw_baseline_results" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="270" y="840" width="190" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="119.5" y="1000" as="sourcePoint"/>
                        <mxPoint x="119.5" y="920" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="5.BaselineResultBook负责解析&lt;br&gt;/tmp/raw_baseline_results目录下的巡检结果并写进excel" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="130" y="940" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="119.5" y="1120" as="sourcePoint"/>
                        <mxPoint x="119.5" y="1040" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="6.最终结果保存在baseline_results_{timestamp}.tar.gz&lt;br&gt;并根据脚本入参决定是否通过邮件发出" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="125" y="1050" width="240" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>