# -*- coding: utf-8 -*-
# !/usr/bin/python

import os
import sys
import getpass
import subprocess
import datetime
import argparse
import xlrd
import yaml

reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
baseline_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(baseline_path))
utils_path = root_path + '/utils'           # ./utils
#resource_path = root_path + '/resources'    # ./resources

sys.path.append(root_path)
sys.path.append(utils_path)
sys.path.append(public_resources)

BASELINE_VAULT_PATH = public_resources + '/baseline_vault'
BASELINE_PLAYBOOK_PATH = baseline_path + '/baseline_playbook.yml'
BASELINE_PLAYBOOK_PATH_RUNTIME = baseline_path + '/baseline_playbook_runtime.yml'

from my_ansible import *
from baseline_xls import *
from utils.my_mail import *
from utils import CMDBApi

timestamp = str(datetime.date.today()).replace('-', '')
local_ip = os.popen('hostname -I').read().split(' ')[0]

class DevManager:
    def __init__(self, name):
        self.name = name
        self.list_a = []
        self.list_b = []

class DevDict:
    def __init__(self, jcssz_devices):
        self.manager_dict = {}

        for dev in jcssz_devices:
            manager_a = dev['USER_MANAGE_A'][0]['nickname']
            manager_b = dev['USER_MANAGE_B'][0]['nickname']

            if manager_a not in self.manager_dict.keys():
                self.manager_dict[manager_a] = DevManager(manager_a)
            
            if manager_b not in self.manager_dict.keys():
                self.manager_dict[manager_b] = DevManager(manager_b)

            self.manager_dict[manager_a].list_a.append(dev['ip'])
            self.manager_dict[manager_b].list_b.append(dev['ip'])

    def items(self):
        return self.manager_dict.items()

    def stat_report(self):
        for name, manager in self.manager_dict.items():
            print(name + ':')
            print('    roleA:{}'.format(len(manager.list_a)))
            print('    roleB:{}'.format(len(manager.list_b)))

def gen_host_ip_mapping():
    hostname_book = xlrd.open_workbook('./baseline_check_files/config/hostname.xls')
    table = hostname_book.sheet_by_index(0)
    hostname_dict = {}
    for row in range(0, table.nrows):
        hostname = table.row_values(row)[0]
        ip_list = table.row_values(row)[1]
        hostname_dict[hostname] = ip_list
    
    subprocess.call('sudo chmod 775 ./baseline_check_files/config', shell=True)
    with open('./baseline_check_files/config/hostname.json', mode='w+') as f:
        json.dump(hostname_dict, f, ensure_ascii=False)

def gen_distrubuting_package():
    #convert excel into json
    gen_host_ip_mapping()
    # prepare baseline check package to be distributed
    subprocess.call('sudo unlink /tmp/baseline_check_files.tar.gz', shell=True)
    subprocess.call('cd {} && sudo tar -zcf /tmp/baseline_check_files.tar.gz ./baseline_check_files'.format(baseline_path), shell=True)

def local_check(args):
    # remove existing results
    subprocess.call('sudo unlink /tmp/baseline_results_{}.tar.gz'.format(timestamp), shell=True)
    subprocess.call('sudo rm -rf /tmp/baseline_results', shell=True)
    subprocess.call('sudo rm -rf /tmp/raw_baseline_results/*', shell=True)
    #execute the script
    subprocess.call('cd /tmp && sudo tar -zxf ./baseline_check_files.tar.gz', shell=True)

    if args.fix:
        cmd = 'cd /tmp/baseline_check_files/ && sudo python ./baseline_fix.py'
    else:
        cmd = 'cd /tmp/baseline_check_files/ && sudo python ./baseline_check.py'

    if args.config:
        config_file = args.config.split('/')[-1]
        cmd += ' --check_json ./config/{}'.format(config_file)

    if args.key:
        cmd += ' --key {}'.format(args.key)

    subprocess.call(cmd, shell=True)

    subprocess.call('sudo mkdir -p /tmp/raw_baseline_results/{}/tmp/baseline_check_files && \
                     sudo mv /tmp/baseline_check_files/result.json /tmp/raw_baseline_results/{}/tmp/baseline_check_files'.format(local_ip, local_ip),
                    shell=True)

    subprocess.call('sudo chmod 666 /tmp/raw_baseline_results/{}/tmp/baseline_check_files/result.json'.format(local_ip, local_ip), shell=True)

    manager_name = 'localhost'

    # error config dump
    manager_error_book = BaselineResultBook(error_only=True, manager=manager_name)
    manager_error_book.aggregate('localhost')
    manager_error_book.dump()

    # all config dump
    # manager_overall_book = BaselineResultBook(error_only=False, manager=manager_name)
    # manager_overall_book.aggregate('localhost')
    # manager_overall_book.dump()

    subprocess.call('cd /tmp && sudo tar -zcvf ./baseline_results_{}.tar.gz ./baseline_results/'.format(timestamp), shell=True)

def get_jcssz_devices():
    from config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host

    cmdb_api = CMDBApi(
        access_key = cmdb_api_access_key,
        secret_key = cmdb_api_secret_key,
        host = cmdb_api_host
    )

    #distribute devices to managers    
    jcssz_devices = cmdb_api.get_host_data(
        query = {
            "status": {"$eq": "运行中"},
            "userGroupManager": "服务器及存储基础设施组",
            "$or": [
                {"osDistro": {"$like": '%red%'}},
                {"osDistro": {"$like": '%centos%'}},
                {"osDistro": {"$like": '%Kylin%'}}
            ],
        },
        fields={
            "USER_MANAGE_A": True, 
            "USER_MANAGE_B": True, 
            "hostname": True,
            "osDistro": True,
            "ip": True
        }
    )
    return jcssz_devices

def gen_runtime_playbook(args):
    config_file = args.config.split('/')[-1]
    key = args.key
    setup = args.setup
    fix = args.fix

    with open(BASELINE_PLAYBOOK_PATH, 'r') as f1:
        playbook = yaml.load_all(f1, Loader=yaml.FullLoader)
        for play in playbook:
            tasks = (play[0]['tasks'])

            # traverse in reverse order for potential removal of element
            for task in tasks[::-1]:
                if task['name'] == 'setup runtime environment' and not setup:
                    tasks.remove(task)
                elif task['name'] == 'run check scripts':
                    if fix:
                        cmd = 'cd /tmp/baseline_check_files/ && sudo python ./baseline_fix.py'
                    else:
                        cmd = 'cd /tmp/baseline_check_files/ && sudo python ./baseline_check.py'
                    
                    if config_file:
                        cmd += ' --check_json /tmp/baseline_check_files/config/{}'.format(config_file)
                    if key:
                        cmd += ' --key {}'.format(key)

                    task['shell'] = cmd

    subprocess.call('sudo chmod 775 {}'.format(baseline_path), shell=True)
    with open(BASELINE_PLAYBOOK_PATH_RUNTIME, 'w') as f2:
        yaml.dump(play, f2, allow_unicode=True)

def gen_ip_dict(jcssz_devices):
    ip_dict = {}
    for dev in jcssz_devices:
        ip = dev.get('ip')
        if ip:
            ip_dict[ip] = dev

    return ip_dict

def global_check(vault_password, args):

    # remove existing results
    subprocess.call('sudo unlink /tmp/baseline_results_{}.tar.gz'.format(timestamp), shell=True)
    subprocess.call('sudo rm -rf /tmp/baseline_results', shell=True)

    # distribute devices to managers
    jcssz_devices = get_jcssz_devices()
    # jcssz_devices = jcssz_devices[0:20]

    dev_by_ip = gen_ip_dict(jcssz_devices)

    dev_dict = DevDict(jcssz_devices)
    # dev_dict.stat_report()

    overall_error_book = BaselineResultBook(error_only=(not args.fix), manager='总览')
    overall_ip_book = BaselineIPBook()
    overall_stat_book = BaselineStatBook()

    gen_runtime_playbook(args)
    
    for manager_name, manager_dev in dev_dict.items():

        if args.user and manager_name != args.user:
            continue

        if not manager_dev.list_a:
            continue

        #ansible inventory doesn't accept local host
        if local_ip in manager_dev.list_a:
            manager_dev.list_a.remove(local_ip)

        if len(manager_dev.list_a) == 1:
            print('The implementation of MyAnsibleService only accepts list with 2 or more elements, skipping devices managed by {}...'.format(manager_name))
            continue

        #delete raw results generated in last iteration
        subprocess.call('sudo rm -rf /tmp/raw_baseline_results/*', shell=True)

        print('checking {} devices managed by {}...'.format(len(manager_dev.list_a), manager_name))

        mas = MyAnsibleService(
            vault_pass=vault_password,
            password_vault_path=BASELINE_VAULT_PATH,
            timeout=1,
            inventory=','.join(manager_dev.list_a),
            forks=16,
        )
        
        """
        playbook steps:
        1. distribute baseline scripts to remote hosts
        2. run baseline script on remote hosts
        3. collect results.json from remote hosts
        """
        mas.playbook(playbooks=[BASELINE_PLAYBOOK_PATH_RUNTIME])

        unreachable_ips = mas.results_callback.get_unreachable_ip()
        failed_ips = mas.results_callback.get_failed_ip()

        # failed ip dump
        manager_ip_book = BaselineIPBook(manager_name)
        manager_ip_book.aggregate(manager_name, dev_by_ip, unreachable_ips, failed_ips)
        manager_ip_book.dump()

        # error config dump
        manager_error_book = BaselineResultBook(error_only=(not args.fix), manager=manager_name)
        manager_error_book.aggregate(manager_name)
        manager_error_book.dump()

        # all config dump
        # manager_overall_book = BaselineResultBook(error_only=False, manager=manager_name)
        # manager_overall_book.aggregate(manager_name)
        # manager_overall_book.dump()

        overall_ip_book.aggregate(manager_name, dev_by_ip, unreachable_ips, failed_ips)
        overall_error_book.aggregate(manager_name)
        overall_stat_book.aggregate(manager_name, len(unreachable_ips), len(failed_ips))
    
    overall_error_book.dump(is_overall=True)
    overall_ip_book.dump()
    overall_stat_book.dump()

    subprocess.call('cd /tmp && sudo tar -zcvf ./baseline_results_{}.tar.gz ./baseline_results/'.format(timestamp), shell=True)

def mail_results():
    from config import mail_user, mail_password, mail_host
    message = []
    message.append(mail_file('/tmp/baseline_results_{}.tar.gz'.format(timestamp), 'x-tar', 'baseline_results_{}.tar.gz'.format(timestamp)))
    send_mail(['<EMAIL>'], 'OS基线巡检结果', contents=message, mail_user=mail_user, mail_password=mail_password, mail_host=mail_host)

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='baseline check script')
    parser.add_argument('-c', '--config', type=str, default='', help='baseline config filename')
    parser.add_argument('-f', '--fix', action='store_true', default=False, help='baseline fix')
    parser.add_argument('-k', '--key', type=str, default='', help='keyword for baseline event')
    parser.add_argument('-l', '--local', action='store_true', default=False, help='check local host')
    parser.add_argument('-m', '--mail', action='store_true', default=False, help='send email')
    parser.add_argument('-s', '--setup', action='store_true', default=False, help='run environemnt setup bash script on remote hosts before executing python script')
    parser.add_argument('-u', '--user', type=str, default='', help='name of manager A')
    
    args = parser.parse_args()

    gen_distrubuting_package()

    if args.local:
        local_check(args)
    else:
        vault_password = getpass.getpass('Please input password for ansible-vault: ')
        global_check(vault_password, args)

    if args.mail:
        mail_results()
