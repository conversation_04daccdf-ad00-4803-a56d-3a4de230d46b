#!/usr/bin/python
# coding=utf-8

import xlwt, os, json, subprocess

BASELINE_FINAL_RESULT_PATH = '/tmp/baseline_results/'
BASELINE_RAW_RESULT_PATH = '/tmp/raw_baseline_results/'
    
class BaselineResultSheet():
    def __init__(self, workbook, sheetname):
        self.row = 0

        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 4000
        self.worksheet.col(1).width = 4000
        self.worksheet.col(2).width = 5500
        self.worksheet.col(3).width = 3000
        self.worksheet.col(4).width = 7000
        self.worksheet.col(5).width = 4000
        self.worksheet.col(6).width = 10000
        self.worksheet.col(7).width = 2000
        self.worksheet.col(8).width = 10000
        self.worksheet.col(9).width = 4000
        self.worksheet.col(10).width = 10000

        self.result_write('主机名', '管理IP', '操作系统', '管理员', '巡检事项', '计划整改时限', '配置项', '结果', '原因', 'plugin类型', '参数')
        #style settings
        # self.style = xlwt.XFStyle()
        # alignment = xlwt.Alignment()
        # alignment.horz = 0x01
        #self.style.alignment = alignment

    def result_write(self, hostname, ip, os, manager, event, date, config, result, reason, plugin, args):
        item_list = [hostname, ip, os, manager, event, date, config, result, reason, plugin, args]
        column = 0
        for item in item_list:
            self.worksheet.write(self.row, column, item)
            column += 1

        self.row += 1

    def baseline_json_dump(self, event, ip, manager, error_only):
        for config in event['configs_result']:
            for arg in config['args_result']:
                if error_only and arg['ans']:
                    continue

                self.result_write(event['hostname'], ip, event['system_platform'], manager, event['event'], 
                                    event['date'], config['name'], str(arg['ans']), arg['des'], config['checker_type'],
                                    str(json.dumps(arg['arg'], indent=4, ensure_ascii=False)))

class BaselineResultBook():
    def __init__(self, error_only, manager = ''):
        self.manager = manager
        self.error_only = error_only
        self.workbook = xlwt.Workbook(encoding='utf-8')
        self.sheet = BaselineResultSheet(self.workbook, '配置列表')

    def aggregate(self, manager):
        for root, dirs, files in os.walk(BASELINE_RAW_RESULT_PATH, topdown = True):
            for file_name in files:
                file_path = os.path.join(root, file_name)
                ip = file_path.split('/')[3] #ip

                with open(file_path, 'r+') as f:
                    events = json.load(f)
                    for event in events:
                        self.sheet.baseline_json_dump(event, ip, manager, self.error_only)

    def dump(self, is_overall=False):
        file_name = ''
        if self.error_only:
            file_name = '异常配置详细列表'
        else:
            file_name = '全量配置详细列表'

        dir = BASELINE_FINAL_RESULT_PATH
        if not is_overall:
            dir += self.manager

        subprocess.call('mkdir -p {}'.format(dir), shell=True)        
        self.workbook.save('{}/{}.xls'.format(dir, file_name))

class BaselineIPSheet():
    def __init__(self, workbook, sheetname):
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 6000
        self.worksheet.col(1).width = 6000
        self.worksheet.col(2).width = 6000
        self.worksheet.col(3).width = 6000
        self.row = 0

        self.result_write('IP', '主机名', '操作系统', '管理员A')

    def result_write(self, ip, hostname, os, manager):
        self.worksheet.write(self.row, 0, ip)
        self.worksheet.write(self.row, 1, hostname)
        self.worksheet.write(self.row, 2, os)
        self.worksheet.write(self.row, 3, manager)
        self.row += 1

class BaselineIPBook():
    def __init__(self, manager = ''):
        self.manager = manager
        self.workbook = xlwt.Workbook(encoding='utf-8')
        self.unreachable_sheet = BaselineIPSheet(self.workbook, '网络连接失败列表')
        self.failed_sheet = BaselineIPSheet(self.workbook, '脚本执行失败列表')

    def aggregate(self, manager, dev_by_ip, unreachable_list = [], failed_list = []):
        for ip in unreachable_list:
            dev = dev_by_ip.get(ip)
            self.unreachable_sheet.result_write(ip, dev.get('hostname'), dev.get('osDistro'), manager)

        for ip in failed_list:
            dev = dev_by_ip.get(ip)
            self.failed_sheet.result_write(ip, dev.get('hostname'), dev.get('osDistro'), manager)

    def dump(self):
        dir = BASELINE_FINAL_RESULT_PATH + self.manager
        subprocess.call('mkdir -p {}'.format(dir), shell=True)
        self.workbook.save('{}/未检查主机列表.xls'.format(dir))

class BaselineHostSheet():
    def __init__(self, workbook, sheetname):
        self.row = 0
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 5000
        self.worksheet.col(1).width = 5000
        self.worksheet.col(2).width = 5000
        self.worksheet.col(3).width = 5000
        self.worksheet.col(4).width = 5000
        self.worksheet.col(5).width = 5000
        self.worksheet.col(6).width = 5000

        #style settings
        self.style = xlwt.XFStyle()
        alignment = xlwt.Alignment()
        alignment.horz = 0x01
        self.style.alignment = alignment

        self.result_write('管理员', '网络不可达机器数量', '脚本执行失败机器数量', '巡检机器数量', '异常机器数量', '巡检配置数量', '异常配置数量')

        #sum accounting
        self.unreachable_host_sum = 0
        self.failed_host_sum = 0
        self.checked_host_sum = 0
        self.error_host_sum = 0
        self.checked_config_sum = 0
        self.error_config_sum = 0

    def result_write(self, manager, unreachable_count, failed_count, checked_host, error_host, checked_config, error_config):
        item_list = [manager, unreachable_count, failed_count, checked_host, error_host, checked_config, error_config]
        column = 0
        for item in item_list:
            self.worksheet.write(self.row, column, item, self.style)
            column += 1

        self.row += 1

        if isinstance(unreachable_count, int):
            #sum accounting
            self.unreachable_host_sum += unreachable_count
            self.failed_host_sum += failed_count
            self.checked_host_sum += checked_host
            self.error_host_sum += error_host
            self.checked_config_sum += checked_config
            self.error_config_sum += error_config

    def sum_write(self):
        self.result_write('总共', self.unreachable_host_sum, self.failed_host_sum, self.checked_host_sum, self.error_host_sum, self.checked_config_sum, self.error_config_sum)

class BaselineKVSheet():
    def __init__(self, workbook, sheetname, keyname):
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 20000
        self.worksheet.col(1).width = 4000
        self.row = 0

        #style settings
        self.style = xlwt.XFStyle()
        alignment = xlwt.Alignment()
        alignment.horz = 0x01
        self.style.alignment = alignment

        self.result_write(keyname, '失败数')

    def result_write(self, key, value):
        self.worksheet.write(self.row, 0, key, self.style)
        self.worksheet.write(self.row, 1, value, self.style)
        self.row += 1

class BaselineStatBook():
    def __init__(self):
        self.workbook = xlwt.Workbook(encoding='utf-8')
        self.host_sheet   = BaselineHostSheet(self.workbook, '主机维度统计')
        self.kernel_sheet = BaselineKVSheet(self.workbook, '操作系统维度统计', '系统版本')
        self.event_sheet  = BaselineKVSheet(self.workbook, '巡检事项粒度统计', '巡检事项')
        self.config_sheet = BaselineKVSheet(self.workbook, '配置项粒度统计',   '配置项')
        self.arg_sheet    = BaselineKVSheet(self.workbook, '配置参数粒度统计', '参数项')
        
        self.kernel_stat = {}
        self.event_stat = {}
        self.config_stat = {}
        self.arg_stat = {}

    def aggregate(self, manager, unreachable_count, failed_count):
        checked_host = 0
        checked_config = 0
        error_host   = 0        
        error_config = 0

        for root, dirs, files in os.walk(BASELINE_RAW_RESULT_PATH, topdown = True):
            for file_name in files:
                host_with_error = False
                checked_host += 1
                file_path = os.path.join(root, file_name)                
                with open(file_path, 'r+') as f:
                    events = json.load(f)
                    for event in events:
                        for config in event['configs_result']:
                            for arg in config['args_result']:
                                checked_config += 1
                                if not arg['ans']:
                                    host_with_error = True
                                    error_config += 1

                                    kernel_version = events[0]['system_platform']
                                    self.kernel_stat[kernel_version] = self.kernel_stat.get(kernel_version, 0) + 1

                                    event_name = event['event']
                                    self.event_stat[event_name] = self.event_stat.get(event_name, 0) + 1

                                    config_name = config['name']                                    
                                    self.config_stat[config_name] = self.config_stat.get(config_name, 0) + 1
                                    
                                    checker = config['checker_type']
                                    args = arg['arg']
                                    args_str = ''
                                    for k, v in args.items():
                                        args_str += k
                                        args_str += '='
                                        args_str += v
                                        args_str += ', '

                                    spec_str = '[{}] {}'.format(checker, args_str)

                                    self.arg_stat[spec_str] = self.arg_stat.get(spec_str, 0) + 1

                if host_with_error:
                    error_host += 1

        #stats for current manager
        self.host_sheet.result_write(manager, unreachable_count, failed_count, checked_host, error_host, checked_config, error_config)
    
    def dump(self):
        self.kernel_stat = sorted(self.kernel_stat.items(), key=lambda x:x[1], reverse=True)
        self.event_stat  = sorted(self.event_stat.items(), key=lambda x:x[1], reverse=True)
        self.config_stat = sorted(self.config_stat.items(), key=lambda x:x[1], reverse=True)
        self.arg_stat    = sorted(self.arg_stat.items(), key=lambda x:x[1], reverse=True)

        for k, v in self.kernel_stat:
            self.kernel_sheet.result_write(k, v)

        for k, v in self.event_stat:
            self.event_sheet.result_write(k, v)

        for k, v in self.config_stat:
            self.config_sheet.result_write(k, v)

        for k, v in self.arg_stat:
            self.arg_sheet.result_write(k, v)

        self.host_sheet.sum_write()
        
        subprocess.call('mkdir -p {}'.format(BASELINE_FINAL_RESULT_PATH), shell=True)
        self.workbook.save('{}/巡检数据统计.xls'.format(BASELINE_FINAL_RESULT_PATH))