# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys, os, openpyxl, subprocess, yaml
reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
hids_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(hids_path)) #../
utils_path = root_path + '/utils' #../utils

sys.path.append(public_resources)
sys.path.append(root_path)
sys.path.append(utils_path)

from utils import my_print

file_path = '/tmp/network_scan.xlsx'

def read_vault_file(vault_file):
    cmd = ['ansible-vault', 'view', vault_file, '--ask-vault-pass']
    process = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        raise Exception(stderr.decode())

    return yaml.safe_load(stdout)

if __name__ == '__main__':
    conn_dict = read_vault_file('/home/<USER>/resources/baseline_vault')
    vault_ips = []
    for ip in conn_dict.keys():
        vault_ips.append(ip) 

    book = openpyxl.load_workbook(file_path)
    #sheet = book.active
    sheet = book['脚本执行失败列表']

    sheet.cell(column=7, row=1).value = '登录信息是否存在'
    for row in range(2, sheet.max_row + 1):
        ip = sheet.cell(column=1, row=row).value.strip()
        sheet.cell(column=7, row=row).value = ip in vault_ips

    subprocess.Popen('sudo chmod 664 {}'.format(file_path), stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    book.save(file_path)