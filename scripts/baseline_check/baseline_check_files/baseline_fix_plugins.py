#!/usr/bin/python
# coding=utf-8
import json
import os
import platform
import re
import socket
import subprocess

from datetime import datetime
from baseline_results import *

BASELINE_FIX_RESULT = 'result.json'

class FixerPlugin:
    name = 'defalut_fixer'

    def __init__(self):
        pass

    def fix(self, baseline_config_item):
        print('set')
        if self.name != baseline_config_item.checker_type:
            return None

class Fixer:
    def __init__(self, event_keyword = ''):
        self.event_keyword = event_keyword
        self.results = []
        self.plugins = {}
        self.ip = self.get_ip()
        self.hostname = socket.gethostname()
        self.system_platform = ' '.join(platform.dist()) + ' (' + platform.uname()[2] + ')'
        self.arch = platform.uname()[-1]

    def register(self, fixer_plugin):
        if fixer_plugin and isinstance(fixer_plugin, FixerPlugin):
            self.plugins[fixer_plugin.name] = fixer_plugin
        else:
            print('plugin {0} registration failed'.format(fixer_plugin.name))

    def get_ip(self):
        stdout, stderr = subprocess.Popen(
            args=['hostname', '-I'],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
        ).communicate()
        return stdout.strip()

    def match(self, baseline_config):
        """根据基线配置，判断机器是否需要执行

        Args:
            baseline_config ([type]): [description]

        Returns:
            [type]: [description]
        """

        if self.event_keyword not in baseline_config.event:
            return False

        # 如果没有正则，则全部匹配
        if not baseline_config.regex:
            return True
        
        match_hostname = re.match(baseline_config.regex.hostname, self.hostname)
        match_ip       = re.match(baseline_config.regex.ip, self.ip)
        match_arch     = re.match(baseline_config.regex.arch, self.arch)

        return match_arch and match_ip and match_hostname
    
    def fix_all(self, baseline_configs):
        for event in baseline_configs:
            self.fix_baseline_event(event)

    def fix_baseline_event(self, baseline_event):        
        if not self.match(baseline_event):
            return

        config_results = []
        for config_item in baseline_event.configs:
            result = self.fix_config_item(config_item)
            if result:
                config_results.append(result)
                
        event_result = baseline_event.__dict__
        event_result.pop('configs')
        event_result['configs_result'] = config_results
        self.results.append(event_result)

    def export_results(self):
        for event_result in self.results:
            # todo name tuple 换成dict
            # event_result['regex'] = dict(event_result.get('regex')._asdict())
            event_result.pop('regex')
            event_result['system_platform'] = self.system_platform
            event_result['hostname'] = self.hostname
            cur_configs = []
            for baseline_item_result in event_result.get('configs_result'):
                # 不受支持的基线检测类型
                # 添加 None
                if not baseline_item_result:
                    cur_configs.append(None)
                    continue
                cur_args_results = []
                for arg_result in baseline_item_result.args_result:
                    cur_args_results.append(arg_result.__dict__)
                baseline_item_result.args_result = cur_args_results
                cur_configs.append(baseline_item_result.__dict__)
            event_result['configs_result'] = cur_configs

        with open(BASELINE_FIX_RESULT, mode='w+') as fin:
            json.dump(self.results, fin, ensure_ascii=False, indent=2)
        os.chmod(BASELINE_FIX_RESULT, 0o644)

    def fix_config_item(self, baseline_config_item):
        plugin = self.plugins.get(baseline_config_item.checker_type)
        if not plugin:
            print('{0} is not registered'.format(baseline_config_item.checker_type))
            return None
        else:
            return plugin.fix(baseline_config_item)

class FileFixPlugin(FixerPlugin):
    name = 'file_checker'

    def fix(self, baseline_config_item):
        if self.name != baseline_config_item.checker_type:
            return None
        config_result = BaselineConfigItemResult(baseline_config_item)
        for arg in baseline_config_item.args:
            pattern = arg.get("pattern")
            target = arg.get("target")
            template = arg.get("template")            
            permission = arg.get('permission')
            owner = arg.get('owner')
            owner_group = arg.get('owner_group')

            if pattern == 'include':
                ans, desc = self.fix_inclusion(target, template)
            elif pattern == 'exact':
                ans, desc = self.fix_exact_match(target, template)
            elif pattern == 'permission':
                ans, desc = self.fix_permission(target, permission, owner, owner_group)
            else:
                print('unrecognized file_checker pattern {0}'.format(pattern))
                return None
            
            args_result = BaselineConfigItemArgsResult(arg, ans, desc)
            config_result.args_result.append(args_result)
            
        return config_result

    def fix_inclusion(self, target, template):
        """
        1.direct copy if target doesn't exist
        """        
        if not os.path.exists(target):
            p1 = subprocess.Popen(
                args=['sudo', 'cp', template, target],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            stdout, stderr = p1.communicate()
            p1.stdout.close()
            p1.stderr.close()

            ans = True
            des = '目标文件不存在，直接复制模板'
            
            if stdout:
                ans = False
                des = '复制过程输出:\n{0}\n'.format(stdout)
            if stderr:
                ans = False
                des = '复制过程错误:\n{0}'.format(stderr)

            return ans, des


        """
        2.check inclusion
        """
        configs_to_match = []
        with open(template, 'r') as tem:
            for line in tem.readlines():
                line = line.strip()
                if line and not line.startswith('#'):
                    #normalize: replace tab/multiple spaces with one single space
                    line = ' '.join(line.split())
                    configs_to_match.append(line.strip())
        try:
            with open(target, 'r') as tar_read:
                for line in tar_read.readlines():
                    line = line.strip()
                    if line and not line.startswith('#'):
                        #normalize: replace tab/multiple spaces with one single space
                        line = ' '.join(line.split())
                        if line in configs_to_match:
                            configs_to_match.remove(line)
        except IOError:
            ans = False
            des = '读取失败'
            return ans, des

        if not len(configs_to_match):
            ans = True
            des = '已包含所有配置，未作修改'
            return ans, des

        """
        3.backup target file
        """
        p1 = subprocess.Popen(
            args=['sudo', 'cp', target, target+'.backup.{0}'.format(str(datetime.now()).split(' ')[0])],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()
        
        if stdout:
            ans = False
            des += '备份过程输出:\n{0}\n'.format(stdout)
            return ans, des
        if stderr:
            ans = False
            des += '备份过程错误:\n{0}'.format(stderr)
            return ans, des

        """
        4.overwrite(only handle key=value type for the time being...)
        4.1 modified existing lines if keyword is found
        4.2 append remaining configs
        """
        ans = True
        des = ''
        config_by_key = {}
        handled_list = []
        unhandled_list = []
        for config in configs_to_match:
            if '=' in config:
                if config.startswith('export'):
                    key = config.split('=')[0].strip('export ').strip()
                else:
                    key = config.split('=')[0].strip()
                config_by_key[key] = config
                handled_list.append(config)
            else:
                unhandled_list.append(config)

        with open(target, 'r') as tar_read:
            lines = tar_read.readlines()
            # handle special case when value occupies servel lines
            skip = False
            identifier = ''
            #update existing kv tuple
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.startswith('#'):
                    if skip:
                        if identifier in line:
                            skip = False
                            identifier = ''
                        lines[i] = ''
                    #only handle key=value type now
                    elif '=' not in line:
                        continue
                    elif line.startswith('export'):
                        key = line.split('=')[0].strip('export ').strip()
                        value = line.split('=')[1].strip()

                        if key in config_by_key.keys():
                            comment = '# updated by baseline_fix at {0}\n'.format(datetime.now())
                            lines[i] = comment + config_by_key[key] + '\n'
                            config_by_key.pop(key)

                            # handle special case when value occupies servel lines
                            if value[0] == '\'' and value.count('\'')%2:
                                identifier = '\''
                                skip = True
                            elif value[0] == '\"' and value.count('\"')%2:
                                identifier = '\"'
                                skip = True
                    else:
                        key = line.split('=')[0].strip()
                        value = line.split('=')[1].strip()
                        if key in config_by_key.keys():
                            comment = '# changed by baseline_fix at {0}\n'.format(datetime.now())
                            lines[i] = comment + config_by_key[key] + '\n'
                            config_by_key.pop(key)
        try:
            with open(target, 'w') as tar_write:
                tar_write.writelines(lines)
                for config in config_by_key.values():
                    comment = '# added by baseline_fix at {0}\n'.format(datetime.now())
                    tar_write.write(comment + config + '\n')

        except IOError:
            ans = False
            des = '写入失败'

        des = '已修改配置：{0}\n'.format('\n'.join(handled_list))
        
        if len(unhandled_list):
            des += '未修改配置: {0}'.format('\n'.join(unhandled_list))
        
        return ans, des

    def fix_exact_match(self, target, template):
        ans = True
        des = ''

        """
        1.check difference
        """
        p1 = subprocess.Popen(
            args=['diff', '-bBupr', template, target, '--ignore-matching-lines=^#'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()
                
        if stderr:
            ans = False
            des += '比对过程中错误:\n{0}'.format(stderr)
            return ans,des

        if not stdout:
            des = '完全匹配，不需要修改'
            return ans,des

        """
        2.backup target file
        """
        p2 = subprocess.Popen(
            args=['sudo', 'cp', target, target+'.backup.{0}'.format(str(datetime.now()).split(' ')[0])],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p2.communicate()
        p2.stdout.close()
        p2.stderr.close()
        
        if stdout:
            ans = False
            des += '备份过程输出:\n{0}\n'.format(stdout)
            return ans, des
        if stderr:
            ans = False
            des += '备份过程错误:\n{0}'.format(stderr)
            return ans, des

        """
        3. replace target file with the template
        """
        p3 = subprocess.Popen(
            args=['sudo', 'cp', template, target],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p3.communicate()
        p3.stdout.close()
        p3.stderr.close()
        
        if stdout:
            ans = False
            des += '复制过程输出:\n{0}\n'.format(stdout)
        if stderr:
            ans = False
            des += '复制过程错误:\n{0}'.format(stderr)

        return ans, des

    def fix_permission(self, target, permission, owner, owner_group):
        
        if not os.path.exists(target):
            return False, '文件不存在'

        if not permission and not owner and not owner_group:
            ans = False
            des = '必须配置至少一项权限检查参数[permission, owner, owner_group]'
            return ans,des

        ans = True
        des = ''

        if permission:
            out, err = self.stat_func('%a', target)            
            if err:
                ans = False
                des = '权限检查错误：\n{0}\n'.format(err)
                return ans, des
            elif out != permission:
                p1 = subprocess.Popen(
                    args=['sudo', 'chmod', permission, target],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                stdout, stderr = p1.communicate()
                p1.stdout.close()
                p1.stderr.close()
                
                if stdout or stderr:
                    ans = False
                    des += 'chmod过程输出:\n{0}\n'.format(stderr+stdout)
                    return ans, des
                
                des += '权限从{}修改为{}\n'.format(out, permission)
        
        if owner:
            out, err = self.stat_func('%U', target)            
            if err:
                ans = False
                des += 'owner检查错误：\n{0}\n'.format(err)
                return ans, des
            elif out != owner:
                p2 = subprocess.Popen(
                    args=['sudo', 'chown', owner, target],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                stdout, stderr = p2.communicate()
                p2.stdout.close()
                p2.stderr.close()
                
                if stdout or stderr:
                    ans = False
                    des += 'chown过程输出:\n{0}\n'.format(stderr+stdout)
                    return ans, des
                
                des += 'owner从{}修改为{}\n'.format(out, owner)

        if owner_group:
            out, err = self.stat_func('%G', target)
            if err:
                ans = False
                des += 'group检查错误：\n{0}'.format(err)
                return ans,des
            elif out != owner_group:
                p3 = subprocess.Popen(
                    args=['sudo', 'chgrp', owner_group, target],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                stdout, stderr = p3.communicate()
                p3.stdout.close()
                p3.stderr.close()
                
                if stdout or stderr:
                    ans = False
                    des += 'chgrp过程输出:\n{0}\n'.format(stderr+stdout)
                    return ans, des
                
                des += 'owner_group从{}修改为{}\n'.format(out, owner_group)

        if not des:
            des = '符合预期，未作修改'

        return ans, des
    
    def stat_func(self, option, target):
        p1 = subprocess.Popen(
            args=['stat', '-c', option, target],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()        
        p1.stdout.close()
        p1.stderr.close()

        return stdout.strip(), stderr.strip()