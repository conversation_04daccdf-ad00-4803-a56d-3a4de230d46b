#!/bin/bash
stop_services="cups postfix pcscd  smb acpid iptables ip6tables rhnsd rhsmcertd"
for serv in $stop_services; do
  echo "-------------------------------------$serv running status:-------------------------------------"
  chkconfig --list $serv >/dev/null 2>&1
  if [ $? -eq 0 ]; then
    chkconfig --list $serv
    service $serv status
  else
    echo "$serv is not installed ,skip~"
  fi
done

exit 0
