#!/bin/bash
grep -v "^#" /etc/fstab | grep -q /var
ret1=$?
grep -v "^#" /etc/fstab | grep -q /tmp
ret2=$?
if [ $ret1 -ne 0 ] && [ $ret2 -ne 0 ]; then
   echo "no separated /var and /tmp mount point ,no need to check."
   exit 0
fi

while read line; do
   if [ "X$line" == "X" ]; then
      continue
   fi
   if echo "$line" | grep "^#" >/dev/null 2>&1; then
      continue
   fi
   if [ "X$line" != "X" ]; then
      mount_point=$(echo $line | awk -F " " '{print $2}')
      mount_para=$(echo $line | awk -F " " '{print $4}')
      if [ "$mount_point" = '/var' ] || [ "$mount_point" = '/tmp' ]; then
         if [ $(echo "$mount_para" | grep "nosuid") ] && [ $(echo "$mount_para" | grep "nodev") ]; then
            echo "check $mount_point $mount_para successfully. "
         else
            echo "check $mount_point $mount_para failed because no nosuid and nodev. "
         fi
      fi
   fi

done </etc/fstab
