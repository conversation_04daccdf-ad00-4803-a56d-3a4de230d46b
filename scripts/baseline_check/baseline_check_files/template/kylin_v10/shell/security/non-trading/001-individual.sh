#!/bin/bash -x

init_date=$(date "+%Y-%m-%d_%H-%M-%S")

grubby --update-kernel=ALL --args="elevator=deadline"
grubby --update-kernel=ALL --args="transparent_hugepage=never"
systemctl set-default multi-user.target

blockdev --setra 16384 /dev/sd* 
echo "blockdev --setra 16384 /dev/sd*" >> /etc/rc.local

systemctl restart sshd
ipmitool user set name 5 jcssz
ipmitool user set password 5 szse@2014
ipmitool user priv 5 4
ipmitool user priv 5 4 1
ipmitool user priv 5 4 2
ipmitool channel setaccess 1 5 callin=on ipmi=true link=on privilege=4
ipmitool channel setaccess 2 5 callin=on ipmi=true link=on privilege=4
ipmitool user enable 5
ipmitool lan set 1 access on
ipmitool lan set 2 access on

chmod +x /etc/rc.d/rc.local
systemctl enable rc-local.service

# systemctl stop NetworkManager
# systemctl disable NetworkManager
systemctl restart chronyd
