#!/bin/bash
stop_services="firewalld bluetooth dbus-org.bluez dbus-org.fedoraproject.FirewallD1 dbus-org.freedesktop.network1 dbus-org.freedesktop.nm-dispatcher dbus-org.freedesktop.timesync1"
for serv in $stop_services; do
   systemctl list-unit-files | grep $serv >/dev/null 2>&1
   if [ $? -eq 0 ]; then
      echo "the $serv running status is $(systemctl is-active $serv) and $(systemctl is-enabled $serv)"
   else
      echo "$serv is not installed,skip~"
   fi
done

echo "ctrl-alt-del.target status is: $(systemctl is-enabled ctrl-alt-del.target)"
exit 0
