# SZSE CHANGE BEGIN
# cmd history format for time stamp
export HISTTIMEFORMAT="%F %T "
# max history cmds
export HISTSIZE=10000
# max history file size
export HISTFILESIZE=10000000
# automatic correct spelling mistakes in directory name
shopt -s cdspell
# use L<PERSON> as seperator for multi-line command in history
shopt -s cmdhist
# save history using append method instead of overwriting
shopt -s histappend
# save shell command to history with all info
export PROMPT_COMMAND='history -a'
# ignore these harmless commands such as ls ps history exit etc when saving history
export HISTIGNORE="&:ls:[bf]g:ps:history:exit"
# timeout of a shell session is set to 30 mins
export TMOUT=6000
# SZSE CHANGE END
