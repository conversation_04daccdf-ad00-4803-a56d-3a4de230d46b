#!/bin/bash

init_date=$(date "+%Y-%m-%d_%H-%M-%S")

if [ -f /var/lib/aide/aide.db.new.gz ]; then
   exit 0
fi

yum install -y aide >/dev/null 2>&1

/usr/sbin/aide --init
tar -czvf /aide-${init_date}.tar.gz /var/lib/aide/aide.db.new.gz /usr/sbin/aide /etc/aide.conf >/dev/null 2>&1

#add the cron task for aide
crontab -u root -l >cron.tmp_aide
task='0 0 * * * root /usr/sbin/aide --check --report=file:/var/log/aide/aide-report-`date +%Y%m%d`.log'
grep "0 0 \* \* \* root /usr/sbin/aide --check" cron.tmp_aide
if [ $? -ne 0 ]; then
   echo "$task" >>cron.tmp_aide
   crontab -u root cron.tmp_aide
fi
rm -f cron.tmp_aide
