# Configuration for systemwide password quality limits
# Defaults:
#
# Number of characters in the new password that must not be present in the
# old password.
# difok = 5
#
# Minimum acceptable size for the new password (plus one if
# credits are not disabled which is the default). (See pam_cracklib manual.)
# Cannot be set to lower value than 6.
# minlen = 9
#
# The maximum credit for having digits in the new password. If less than 0
# it is the minimum number of digits in the new password.
# dcredit = 1
#
# The maximum credit for having uppercase characters in the new password.
# If less than 0 it is the minimum number of uppercase characters in the new
# password.
# ucredit = 1
#
# The maximum credit for having lowercase characters in the new password.
# If less than 0 it is the minimum number of lowercase characters in the new
# password.
# lcredit = 1
#
# The maximum credit for having other characters in the new password.
# If less than 0 it is the minimum number of other characters in the new
# password.
# ocredit = 1
#
# The minimum number of required classes of characters for the new
# password (digits, uppercase, lowercase, others).
# minclass = 0
#
# The maximum number of allowed consecutive same characters in the new password.
# The check is disabled if the value is 0.
# maxrepeat = 0
#
# The maximum number of allowed consecutive characters of the same class in the
# new password.
# The check is disabled if the value is 0.
# maxclassrepeat = 0
#
# Whether to check for the words from the passwd entry GECOS string of the user.
# The check is enabled if the value is not 0.
# gecoscheck = 0
#
# Path to the cracklib dictionaries. Default is to use the cracklib default.
# dictpath =

# SZSE CHANGE BEGIN
# Per CCE-CCE-27333-4: Set maxrepeat = 2 in /etc/security/pwquality.conf
maxrepeat = 2
# Per CCE-CCE-27214-6: Set dcredit = -1 in /etc/security/pwquality.conf
dcredit = -1
# Per CCE-CCE-27293-0: Set minlen = 15 in /etc/security/pwquality.conf
minlen = 12
# Per CCE-CCE-27200-5: Set ucredit = -1 in /etc/security/pwquality.conf
ucredit = -1
# Per CCE-CCE-27360-7: Set ocredit = -1 in /etc/security/pwquality.conf
ocredit = -1
# Per CCE-CCE-27345-8: Set lcredit = -1 in /etc/security/pwquality.conf
lcredit = -1
# Per CCE-CCE-26631-2: Set difok = 5 in /etc/security/pwquality.conf
difok = 5
# Per CCE-CCE-27115-5: Set minclass = 4 in /etc/security/pwquality.conf
minclass = 4
# SZSE CHANGE END
