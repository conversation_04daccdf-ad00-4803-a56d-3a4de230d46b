# kickstart template for Fedora 8 and later.
# (includes %end blocks)
# do not use with earlier distros

#platform=x86, AMD64, or Intel EM64T
# System authorization information
auth  --useshadow  --enablemd5
# System bootloader configuration
bootloader --location=mbr --append="crashkernel=256M"
# Partition clearing information
clearpart --all --initlabel
# Use text mode install
text
# Firewall configuration
firewall --enabled
# Run the Setup Agent on first boot
firstboot --disable
# System keyboard
keyboard us
# System language
lang en_US
# Use network installation
<%= @mediapath %>
# Network configuration
# network --bootproto=dhcp --device=eth0 --nameserver=<%= @host.subnet.dns_primary %> --hostname=<%= @host %>
network --bootproto <%= "static --ip=#{@host.ip} --netmask=#{@host.subnet.mask} --gateway=#{@host.subnet.gateway} --nameserver=#{[@host.subnet.dns_primary,@host.subnet.dns_secondary].reject{|n| n.blank?}.join(',')}" %> --hostname <%= @host %>
# Reboot after installation
reboot

#Root password
rootpw --iscrypted <%= root_pass %>
# SELinux configuration
selinux --disabled
# Do not configure the X Window System
skipx
# System timezone
timezone  Asia/Shanghai
# Install OS instead of upgrade
install
# Clear the Master Boot Record
zerombr
# Allow anaconda to partition the system as needed
# autopart
part /boot --fstype=ext4 --size=512
part pv.01 --size=100 --grow
volgroup vg_system pv.01
logvol / --fstype=ext4 --vgname=vg_system --name=lv_root --size=10240
logvol swap --fstype=ext4 --vgname=vg_system --name=lv_swap --size=4096 
logvol /home --fstype=ext4 --vgname=vg_system --name=lv_home --size=20480
#enable and disable some service 
services --enable acpid,smartd,sysstat,auditd,ipmi,irqbalance,kdump,rsyslog --disable rhnsd,rhsmcertd,netfs,iptables,postfix,sendmail,mdmoniter,abrtd

%packages
@base
@core
pexpect
python-dmidecode
perl-XML-Twig
screen
mcelog
ipmitool
-alsa-utils
-quota
-redhat-indexhtml
-Red_Hat_Enterprice_Linux-Release_Notes-6-en-US
-abrt-addon-ccpp
-abrt-addon-kerneloops
-abrt-cli
-abrt-plugin-mailx
-abrt-plugin-sosreport
-b43-fwcutter
-cpuspeed
-cryptsetup-luks
-dmraid
-fprintd-pam
-gnupg2
-hunspell
-hunspell-en
-irqblance
-ledmon
-mdadm
-nano
-pcmciautils
-rdate
-redhat-support-tool
-rfkill
-setuptool
-system-config-firewall-tui
-system-config-network-tui
-tcsh
-virt-what
-wireless-tools
-iptables-ipv6
-redhat-support-tool
-rhnsd
-subscription-manager
-yum-rhn-plugin
-aic94xx-firmware
-atmel-firmware
-b43-openfwwf
-bfa-firmware
-ipw2100-firmware
-ipw2200-firmware
-ivtv-firmware
-iwl100-firmware
-iwl1000-firmware
-iwl3945-firmware
-iwl4965-firmware
-iwl5000-firmware
-iwl5010-firmware
-iwl6000-firmware
-iwl6000g2a-firmware
-iwl6050-firmware
-libertas-usb8388-firmware
-postfix
-ql2100-firmware
-ql2200-firmware
-ql23xx-firmware
-ql2400-firmware
-ql2500-firmware
-rt61pci-firmware
-rt73usb-firmware
-xorg-x11-drv-ati-firmware
-yaboot
-zd1211-firmware
%end

%post
logger "Starting anaconda <%= @host %> postinstall"
exec < /dev/tty3 > /dev/tty3
#changing to VT 3 so that we can see whats going on....
/usr/bin/chvt 3
(
# config system files with default template.
pushd /
wget -r -np -nH -R "index.*" <%= @host.params['puppet_server'] %>:81/home/<USER>/linux/6.6/config/default/
sleep 5
wget -r -np -nH -R "index.*" <%= @host.params['puppet_server'] %>:81/home/<USER>/linux/6.6/shell/security/
sleep 3
popd

#update local time
echo "updating system time"
/usr/sbin/ntpdate <%= @host.params['puppet_server'] %>
hwclock -w

cat > /etc/yum.repos.d/rhel_foreman.repo << EOF1
[rhel6.6]
name=rhel6.6
baseurl=http://<%= @host.params['puppet_server'] %>:81/rhel6/server
gpgcheck=0
enabled=1
[rpm]
name=rpm
baseurl=http://<%= @host.params['puppet_server'] %>:81/rpm
gpgcheck=0
enabled=1
EOF1

# add the puppet package
yum -t -y -e 0 install puppet
echo "Configuring puppet"
cat > /etc/puppet/puppet.conf << EOF
<%= snippet 'puppet.conf' %>
EOF

# Setup puppet to run on system reboot
/sbin/chkconfig --level 345 puppet on
/usr/bin/puppet agent --config /etc/puppet/puppet.conf -o --tags no_such_tag --server <%= @host.puppetmaster %> --no-daemonize
sync

# Inform the build system that we are done.
echo "Informing Foreman that we are built"
wget -q -O /dev/null --no-check-certificate <%= foreman_url %>
# Sleeping an hour for debug
) 2>&1 | tee /root/install.post.log
exit 0
%end
