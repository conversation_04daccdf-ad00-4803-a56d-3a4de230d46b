#!/usr/bin/python
# coding=utf-8

import xlwt

class BaselineResultBook():
    def __init__(self, bookpath):
        self.bookpath = bookpath
        self.workbook = xlwt.Workbook(encoding='utf-8')
        
    def result_save(self):
        self.workbook.save(self.bookpath)

class BaselineResultSheet():
    def __init__(self, workbook, sheetname):
        #sheet settings
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 7000
        self.worksheet.col(1).width = 10000
        self.worksheet.col(2).width = 10000
        self.worksheet.col(3).width = 2000
        self.worksheet.col(4).width = 10000
        self.worksheet.col(5).width = 10000

        #style settings
        # self.style = xlwt.XFStyle()
        # alignment = xlwt.Alignment()
        # alignment.horz = 0x01
        #self.style.alignment = alignment

        self.row = 0
        self.result_write('巡检事务', '配置项', '描述', '结果', '原因', '参数')        

    def result_write(self, event, config, desc, result, reason, args):
        self.worksheet.write(self.row, 0, event)
        self.worksheet.write(self.row, 1, config)
        self.worksheet.write(self.row, 2, desc)
        self.worksheet.write(self.row, 3, result)
        self.worksheet.write(self.row, 4, reason)
        self.worksheet.write(self.row, 5, args)
        self.row += 1

    # def baseline_result_dump(self, result):
    #     for config in result.configs:
    #         for arg in config.args_result:
    #             self.result_write(result.event, config.name, config.description, arg.ans, arg.des, str(arg.arg))

    def baseline_json_dump(self, event, error_only = False):
        
        for config in event['configs_result']:
            for arg in config['args_result']:
                if error_only and arg['ans']:
                    continue

                self.result_write(event['name'], config['name'], config['description'], str(arg['ans']), arg['des'], str(arg['arg']))
