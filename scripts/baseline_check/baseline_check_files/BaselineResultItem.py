#!/usr/bin/python
# coding=utf-8


class BaselineConfigItemResult:
    def __init__(self, baseline_config_item):
        self.name = baseline_config_item.name
        self.category = baseline_config_item.category
        self.description = baseline_config_item.description
        self.args_result = []

    def __str__(self):
        return 'name:{} configs:{}'.format(self.name, self.args_result)

    def __repr__(self):
        return self.__str__()


class BaselineConfigItemArgsResult:
    def __init__(self, baseline_config_item_arg, ans=""):
        self.arg = baseline_config_item_arg
        self.ans = ""
        self.des = ""

    def __str__(self):
        return 'arg:{} ans:{} des:{}'.format(self.arg, self.ans, self.des)

    def __repr__(self):
        return self.__str__()
