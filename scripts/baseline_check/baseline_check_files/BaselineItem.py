#!/usr/bin/python
# coding=utf-8
import json
from collections import namedtuple

BaselineRegex = namedtuple('BaselineRegex', ['hostname', 'app_id', 'ip'])


class BaselineConfigureItem:
    def __init__(self, name, category, args, description, **kwargs):
        self.name, self.category, self.args, self.description = name, category, args, description

    def __str__(self):
        return "name:{} category:{} des:{}".format(self.name, self.category, self.description)

    def __repr__(self):
        return self.__str__()


class BaselineConfig:
    def __init__(self, name, manager_name, implementer, regex=None, system_platform='', level='', configs=None, *args,
                 **kwargs):
        self.name, self.manager_name, self.implementer, \
        self.regex, self.system_platform, self.level = \
            name, manager_name, implementer, regex, system_platform, level
        self.configs = []
        for _ in configs:
            self.configs.append(BaselineConfigureItem(**_))
        # todo dict
        self.regex = namedtuple('BaselineRegex', regex.keys())(*regex.values())

    def __str__(self):
        return 'name:{} configs:{}'.format(self.name, self.configs)

    def __repr__(self):
        return self.__str__()


def _decode_list(data):
    rv = []
    for item in data:
        if isinstance(item, unicode):
            item = item.encode('utf8')
        elif isinstance(item, list):
            item = _decode_list(item)
        elif isinstance(item, dict):
            item = _decode_dict(item)
        rv.append(item)
    return rv


def _decode_dict(data):
    rv = {}
    for k, v in data.items():
        if isinstance(k, unicode):
            k = k.encode('utf8')
        if isinstance(v, unicode):
            v = v.encode('utf8')
        elif isinstance(v, list):
            v = _decode_list(v)
        elif isinstance(v, dict):
            v = _decode_dict(v)
        rv[k] = v
    return rv


class BaselineConfigLoader:
    def __init__(self, path):
        self.path = path

    def load(self):
        res = []
        with open(self.path) as fin:
            data = json.load(fin, object_hook=_decode_dict)
        if not data:
            return
        for json_d in data:
            cur_baseline_config = BaselineConfig(**json_d)
            res.append(cur_baseline_config)
        return res
