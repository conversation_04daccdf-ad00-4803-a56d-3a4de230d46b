#!/bin/bash
chmod 400 /etc/crontab 
chmod 400 /etc/securetty 
chmod 600 /etc/inittab 
chmod 600 /boot/grub2/grub.cfg
chmod 600 /etc/sysctl.conf
chmod 640 /etc/login.defs 
chmod 644 /etc/hosts.allow
chmod 644 /etc/hosts.deny
chmod 600 /root/.bash_profile 
chmod 600 /root/.bashrc 
chmod 600 /root/.cshrc
chmod 640 /etc/login.defs
#rpm -aq|grep audit >/tmp/audit.log 2>&1
#ls -l /etc/audit/ >> /tmp/audit.log 2>&1
#ls -l /etc/audit/audit.rules >> /tmp/audit.log 2>&1
chmod 640 /etc/audit/audit.rules
chmod 600 /etc/ssh/sshd_config
chown root:root /etc/passwd /etc/shadow /etc/group /etc/gshadow

