
# SZSE CHANGE BEGIN
# for stsv5 enable some sudo cmds
Cmnd_Alias SZSE_CMDS = /usr/sbin/dmidecode, /sbin/lspci, /bin/netstat, /usr/sbin/nethogs, /usr/bin/ipmitool, /sbin/ifconfig, /sbin/service, /sbin/route, /usr/sbin/hpacucli, /sbin/arp, /sbin/ethtool, /bin/egrep, /sbin/sysctl, /bin/grep, /bin/mkdir, /sbin/service, /usr/sbin/ntpd, /usr/sbin/ntpdate, /usr/sbin/ntpq, /sbin/chkconfig, /bin/ln, /sbin/mount.glusterfs, /bin/mount ,/bin/cp ,/bin/umount
#, /usr/bin/iotop
#stsv5 ALL=(ALL) NOPASSWD:SZSE_CMDS
stsv5 ALL=(ALL) NOPASSWD:ALL

# SZSE CHANGE END
