[{"name": "redhat_6 PAM配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/pam.d/system-auth配置检查", "category": "path_config_need", "description": "检查system-auth标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/system-auth", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/system-auth"}]}]}, {"name": "redhat_7 PAM配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/pam.d/password-auth-ac配置检查", "category": "path_config_need", "description": "检查access.rules标准基线配置项是否存在", "args": [{"src": "/etc/pam.d/password-auth-ac", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/pam.d/password-auth-ac"}]}, {"name": "/etc/pam.d/system-auth-ac配置检查", "category": "path_config_need", "description": "检查system-auth-ac标准基线配置项是否存在", "args": [{"src": "/etc/pam.d/system-auth-ac", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/pam.d/system-auth-ac"}]}]}, {"name": "redhat_7 security配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/security/pwquality.conf配置检查", "category": "path_config_need", "description": "检查pwquality.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/pwquality.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/pwquality.conf"}]}]}, {"name": "kylin_10 PAM配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/pam.d/password-auth配置检查", "category": "path_config_need", "description": "检查password-auth标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/password-auth", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/password-auth"}]}, {"name": "/etc/pam.d/system-auth配置检查", "category": "path_config_need", "description": "检查system-auth标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/system-auth", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/system-auth"}]}]}, {"name": "kylin_10 security配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/security/pwquality.conf配置检查", "category": "path_config_need", "description": "检查pwquality.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/pwquality.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/pwquality.conf"}]}]}]