#!/usr/bin/python
# coding=utf-8
import json
from collections import namedtuple

class BaselineConfig:
    def __init__(self, name, checker_type, description, args, **kwargs):
        self.name, self.checker_type, self.description, self.args = name, checker_type, description, args

    def __str__(self):
        return "name:{} checker_type:{} description:{}".format(self.name, self.checker_type, self.description)

    def __repr__(self):
        return self.__str__()


class BaselineEvent:
    def __init__(self, event, system_platform='', level='', date='', regex=None, configs=None, *args,
                 **kwargs):
        self.event, self.system_platform, self.level, self.date, self.regex = event, system_platform, level, date, regex
        self.configs = []
        for config in configs:
            self.configs.append(BaselineConfig(**config))

        self.regex = namedtuple('BaselineRegex', regex.keys())(*regex.values())

    def __str__(self):
        return 'event:{} regex:{} configs:{}'.format(self.event, self.regex, self.configs)

    def __repr__(self):
        return self.__str__()

class BaselineConfigLoader:
    def __init__(self, path):
        self.path = path

    def load(self):
        event_list = []
        with open(self.path) as fin:
            data = json.load(fin, object_hook=_decode_dict)
        if not data:
            return
        for json_d in data:
            baseline_event = BaselineEvent(**json_d)
            event_list.append(baseline_event)
        return event_list

def _decode_list(data):
    rv = []
    for item in data:
        if isinstance(item, unicode):
            item = item.encode('utf8')
        elif isinstance(item, list):
            item = _decode_list(item)
        elif isinstance(item, dict):
            item = _decode_dict(item)
        rv.append(item)
    return rv

def _decode_dict(data):
    rv = {}
    for k, v in data.items():
        if isinstance(k, unicode):
            k = k.encode('utf8')
        if isinstance(v, unicode):
            v = v.encode('utf8')
        elif isinstance(v, list):
            v = _decode_list(v)
        elif isinstance(v, dict):
            v = _decode_dict(v)
        rv[k] = v
    return rv