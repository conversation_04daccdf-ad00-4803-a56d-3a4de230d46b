[{"event": "redhat_7 主机名IP映射检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "主机名IP映射检查", "description": "hostname和IP列表精准匹配", "checker_type": "hostname_check", "args": [{}]}]}, {"event": "密码审计基线", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "配置文件检测", "description": "目标文件路径需要同源文件路径一致", "checker_type": "path_compare", "args": [{"dest": "/etc/pam.d/su", "src": "./template/redhat_7/config/default/etc/pam.d/su"}, {"dest": "/etc/pam.d/su-l", "src": "./template/redhat_7/config/default/etc/pam.d/su-l"}, {"dest": "/etc/pam.d/password-auth-ac", "src": "./template/redhat_7/config/default/etc/pam.d/password-auth-ac", "ignore_comments": "True"}, {"dest": "/etc/pam.d/system-auth-ac", "src": "./template/redhat_7/config/default/etc/pam.d/system-auth-ac"}, {"dest": "/etc/security/pwquality.conf", "src": "./template/redhat_7/config/default/etc/security/pwquality.conf"}]}, {"name": "配置文件检测", "description": "config字符串需要在src文件路径中存在", "checker_type": "config_need", "args": [{"src": "/etc/security/pwquality.conf", "config": "minlen = 12"}]}, {"name": "密码策略文件权限检查", "description": "目标路径存在且权限一致", "checker_type": "path_permission", "args": [{"owner": "root", "src": "/etc/passwd", "owner_group": "root", "permission": "644"}, {"owner": "root", "src": "/etc/shadow", "owner_group": "root", "permission": "000"}]}]}, {"event": "服务器通用基线", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "HIDS进程巡检", "description": "HIDS进程是否正在运行", "checker_type": "process_check", "args": [{"process_trait": "titanagent"}]}, {"name": "Flume进程巡检", "description": "Flume日志检测进程是否正在运行", "checker_type": "process_check", "args": [{"process_trait": "flume"}]}, {"name": "node_exporter", "description": "node-exporter巡检", "checker_type": "process_check", "args": [{"process_trait": "/home/<USER>/node_exporter", "permission": "root"}]}, {"name": "process_exporter", "description": "process_exporter巡检", "checker_type": "process_check", "args": [{"process_trait": "process-exporter"}]}, {"name": "定时任务配置文件", "description": "目标路径存在且权限一致", "checker_type": "path_permission", "args": [{"owner": "", "src": "/etc/crontab", "owner_group": "", "permission": "400"}]}, {"name": "Flume安装动作检测", "description": "系统管理员是否安装flume", "checker_type": "path_permission", "args": [{"owner": "logadmin", "src": "/home/<USER>/flume", "owner_group": "logadmin", "permission": ""}]}, {"name": "HIDS安装动作检测", "description": "系统管理员是否安装flume", "checker_type": "path_permission", "args": [{"owner": "root", "src": "/etc/titanagent/", "owner_group": "root", "permission": "700"}]}, {"name": "ADDM安装动作检测", "description": "系统管理员是否安装ADDM", "checker_type": "path_permission", "args": [{"owner": "ADDMAdmin", "src": "/home/<USER>/", "owner_group": "ADDMAdmin", "permission": "700"}]}]}, {"event": "redhat_7 audit配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/audit/rules.d/access.rules配置检查", "description": "检查access.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/access.rules", "src": "/etc/audit/rules.d/access.rules"}]}, {"name": "/etc/audit/rules.d/actions.rules配置检查", "description": "检查actions.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/actions.rules", "src": "/etc/audit/rules.d/actions.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules配置检查", "description": "检查audit_rules_networkconfig_modification.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/audit_rules_networkconfig_modification.rules", "src": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules配置检查", "description": "检查audit_rules_usergroup_modification.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/audit_rules_usergroup_modification.rules", "src": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_time_rules.rules配置检查", "description": "检查audit_time_rules.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/audit_time_rules.rules", "src": "/etc/audit/rules.d/audit_time_rules.rules"}]}, {"name": "/etc/audit/rules.d/delete.rules配置检查", "description": "检查delete.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/delete.rules", "src": "/etc/audit/rules.d/delete.rules"}]}, {"name": "/etc/audit/rules.d/export.rules配置检查", "description": "检查export.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/export.rules", "src": "/etc/audit/rules.d/export.rules"}]}, {"name": "/etc/audit/rules.d/immutable.rules配置检查", "description": "检查immutable.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/immutable.rules", "src": "/etc/audit/rules.d/immutable.rules"}]}, {"name": "/etc/audit/rules.d/logins.rules配置检查", "description": "检查logins.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/logins.rules", "src": "/etc/audit/rules.d/logins.rules"}]}, {"name": "/etc/audit/rules.d/MAC-policy.rules配置检查", "description": "检查MAC-policy.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/MAC-policy.rules", "src": "/etc/audit/rules.d/MAC-policy.rules"}]}, {"name": "/etc/audit/rules.d/modules.rules配置检查", "description": "检查modules.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/modules.rules", "src": "/etc/audit/rules.d/modules.rules"}]}, {"name": "/etc/audit/rules.d/perm_mod.rules配置检查", "description": "检查perm_mod.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/perm_mod.rules", "src": "/etc/audit/rules.d/perm_mod.rules"}]}, {"name": "/etc/audit/rules.d/privileged.rules配置检查", "description": "检查privileged.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/privileged.rules", "src": "/etc/audit/rules.d/privileged.rules"}]}, {"name": "/etc/audit/rules.d/session.rules配置检查", "description": "检查session.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/session.rules", "src": "/etc/audit/rules.d/session.rules"}]}, {"name": "/etc/audit/rules.d/time-change.rules配置检查", "description": "检查time-change.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/rules.d/time-change.rules", "src": "/etc/audit/rules.d/time-change.rules"}]}, {"name": "/etc/audit/audit-stop.rules配置检查", "description": "检查time-change.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/audit/audit-stop.rules", "src": "/etc/audit/audit-stop.rules"}]}]}, {"event": "redhat_7 PAM配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/pam.d/password-auth-ac配置检查", "description": "检查access.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/pam.d/password-auth-ac", "src": "/etc/pam.d/password-auth-ac", "ignore_comments": "True"}]}, {"name": "/etc/pam.d/su配置检查", "description": "检查su标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/pam.d/su", "src": "/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "description": "检查su-l标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/pam.d/su-l", "src": "/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth-ac配置检查", "description": "检查system-auth-ac标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/pam.d/system-auth-ac", "src": "/etc/pam.d/system-auth-ac"}]}]}, {"event": "redhat_7 profile.d配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/profile.d/szse.sh配置检查", "description": "检查access.rules标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/profile.d/szse.sh", "src": "/etc/profile.d/szse.sh"}]}]}, {"event": "redhat_7 security配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/security/limits.conf配置检查", "description": "检查limits.conf标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/security/limits.conf", "src": "/etc/security/limits.conf"}]}, {"name": "/etc/security/pwquality.conf配置检查", "description": "检查pwquality.conf标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/security/pwquality.conf", "src": "/etc/security/pwquality.conf"}]}, {"name": "/etc/security/limits.d/20-nproc.conf配置检查", "description": "检查20-nproc.conf标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/security/limits.d/20-nproc.conf", "src": "/etc/security/limits.d/20-nproc.conf"}]}]}, {"event": "redhat_7 selinux配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/selinux/config配置检查", "description": "检查config标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/selinux/config", "src": "/etc/selinux/config"}]}]}, {"event": "redhat_7 ssh配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "description": "检查sshd_config标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/ssh/sshd_config", "src": "/etc/ssh/sshd_config"}]}]}, {"event": "redhat_7 sysctl.d配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/sysctl.d/10-szse.conf配置检查", "description": "检查sshd_config标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/sysctl.d/10-szse.conf", "src": "/etc/sysctl.d/10-szse.conf"}]}]}, {"event": "redhat_7 etc配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/chrony.conf配置检查", "description": "检查chrony.conf标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/chrony.conf", "src": "/etc/chrony.conf"}]}, {"name": "/etc/host.conf配置检查", "description": "检查host.conf标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/host.conf", "src": "/etc/host.conf"}]}, {"name": "/etc/login.defs配置检查", "description": "检查login.defs标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/login.defs", "src": "/etc/login.defs"}]}, {"name": "/etc/sudoers配置检查", "description": "检查sudoers标准基线配置项是否存在", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_7/config/default/etc/sudoers", "src": "/etc/sudoers"}]}]}]