#!/usr/bin/python
# coding=utf-8
import json, sys
from collections import OrderedDict

reload(sys)
sys.setdefaultencoding('utf-8')

KYLIN10_BASELINE_PATH = './config/kylin_v10.json'
RHEL6_BASELINE_PATH = './config/redhat_6.json'
RHEL7_BASELINE_PATH = './config/redhat_7.json'

RHEL6_BASELINE_PATH_NEW = './config/new_redhat_6.json'
RHEL7_BASELINE_PATH_NEW = './config/new_redhat_7.json'
KYLIN10_BASELINE_PATH_NEW = './config/new_kylin_v10.json'

if __name__ == '__main__':
    with open(RHEL7_BASELINE_PATH) as f:
        events = json.load(f)

    redhat6_list = []
    redhat7_list = []
    kylin10_list = []

    for event in events:
        new = OrderedDict()
        new['event'] = event['event']
        new['level'] = event['level']
        new['date'] = event['date']
        new['regex'] = event['regex']
        new['configs'] = []
        
        for config in event['configs']:
            new_config = OrderedDict()
            new_config['name'] = config['name']
            new_config['description'] = config['description']
            
            if config['checker_type'] == 'process_check':
                new_config['checker_type'] = 'process_checker'
                new_config['args'] = config['args']

            if config['checker_type'] == 'hostname_check':
                new_config['checker_type'] = 'hostname_checker'
                new_config['args'] = config['args']

            if config['checker_type'] == 'path_compare':
                new_config['checker_type'] = 'file_checker'
                new_config['args'] = []              
                for arg in config['args']:
                    new_arg = OrderedDict()
                    new_arg['pattern'] = 'exact'
                    new_arg['target'] = arg['dest']
                    new_arg['template'] = arg['src']
                    new_config['args'].append(new_arg)

            if config['checker_type'] == 'path_config_need':
                new_config['checker_type'] = 'file_checker'
                new_config['args'] = []
                for arg in config['args']:
                    new_arg = OrderedDict()
                    new_arg['pattern'] = 'include'
                    new_arg['target'] = arg['src']
                    new_arg['template'] = arg['dest']
                    new_config['args'].append(new_arg)

            if config['checker_type'] == 'path_permission':
                new_config['checker_type'] = 'file_checker'
                new_config['args'] = []
                for arg in config['args']:
                    new_arg = OrderedDict()
                    new_arg['pattern'] = 'permission'
                    new_arg['target'] = arg['src']
                    new_arg['owner'] = arg['owner']
                    new_arg['owner_group'] = arg['owner_group']
                    new_arg['permission'] = arg['permission']
                    new_config['args'].append(new_arg)

            new['configs'].append(new_config)

        redhat6_list.append(new)

    with open(RHEL7_BASELINE_PATH_NEW, mode='w+') as f:
        json.dump(redhat6_list, f, ensure_ascii=False, indent=2)

    sys.exit(0)

    redhat6_list = []
    redhat7_list = []
    kylin10_list = []

    for event in events:
        
        new = OrderedDict()
        new['event'] = event['name']        
        new['level'] = event['level']
        new['date'] =  '2025/03/01'
        new['regex'] = event['regex']
        new['configs'] = []

        for config in event['configs']:
            new_config = OrderedDict()
            new_config['name'] = config['name']
            new_config['description'] = config['description']
            new_config['checker_type'] = config['category']
            for arg in config['args']:
                for key, value in arg.items():
                    value = value.replace('redhat_6', 'template/redhat_6')
                    value = value.replace('redhat_7', 'template/redhat_7')
                    value = value.replace('kylin_v10', 'template/kylin_v10')
                    value = value.replace('/tmp/baseline_check_files', '.')
                    arg[key] = value

            new_config['args'] = config['args']
            new['configs'].append(new_config)
        
        if event['system_platform'] == '6':
            redhat6_list.append(new)
        if event['system_platform'] == '7':
            redhat7_list.append(new)
        if event['system_platform'] == '10':
            kylin10_list.append(new)
        if event['system_platform'] == '*':
            redhat6_list.append(new)
            redhat7_list.append(new)
            kylin10_list.append(new)

    with open(RHEL6_BASELINE_PATH, mode='w+') as f:
        json.dump(redhat6_list, f, ensure_ascii=False, indent=2)
    
    with open(RHEL7_BASELINE_PATH, mode='w+') as f:
        json.dump(redhat7_list, f, ensure_ascii=False, indent=2)
    
    with open(KYLIN10_BASELINE_PATH, mode='w+') as f:
        json.dump(kylin10_list, f, ensure_ascii=False, indent=2)
