[{"name": "redhat_7 主机名IP映射检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "date": "2025/03/01", "configs": [{"name": "主机名IP映射检查", "category": "hostname_check", "description": "hostname和IP列表精准匹配", "args": [{}]}]}, {"name": "redhat_6 主机名IP映射检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "主机名IP映射检查", "category": "hostname_check", "description": "hostname和IP列表精准匹配", "args": [{}]}]}, {"name": "kylin_10 主机名IP映射检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "主机名IP映射检查", "category": "hostname_check", "description": "hostname和IP列表精准匹配", "args": [{}]}]}, {"name": "密码审计基线", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "配置文件检测", "category": "path_compare", "description": "目标文件路径需要同源文件路径一致", "args": [{"src": "./redhat_7/config/default/etc/pam.d/su", "dest": "/etc/pam.d/su"}, {"src": "./redhat_7/config/default/etc/pam.d/su-l", "dest": "/etc/pam.d/su-l"}, {"src": "./redhat_7/config/default/etc/pam.d/password-auth-ac", "dest": "/etc/pam.d/password-auth-ac", "ignore_comments": "True"}, {"src": "./redhat_7/config/default/etc/pam.d/system-auth-ac", "dest": "/etc/pam.d/system-auth-ac"}, {"src": "./redhat_7/config/default/etc/security/pwquality.conf", "dest": "/etc/security/pwquality.conf"}]}, {"name": "配置文件检测", "category": "config_need", "description": "config字符串需要在src文件路径中存在", "args": [{"src": "/etc/security/pwquality.conf", "config": "minlen = 12"}]}, {"name": "密码策略文件权限检查", "category": "path_permission", "description": "目标路径存在且权限一致", "args": [{"src": "/etc/passwd", "permission": "644", "owner": "root", "owner_group": "root"}, {"src": "/etc/shadow", "permission": "000", "owner": "root", "owner_group": "root"}]}]}, {"name": "密码审计基线", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "配置文件检测", "category": "path_compare", "description": "目标文件路径需要同源文件路径一致", "args": [{"src": "./redhat_6/config/default/etc/pam.d/su", "dest": "/etc/pam.d/su"}, {"src": "./redhat_6/config/default/etc/pam.d/su-l", "dest": "/etc/pam.d/su-l"}, {"src": "./redhat_6/config/default/etc/pam.d/system-auth", "dest": "/etc/pam.d/system-auth"}]}, {"name": "密码策略文件权限检查", "category": "path_permission", "description": "目标路径存在且权限一致", "args": [{"src": "/etc/passwd", "permission": "644", "owner": "root", "owner_group": "root"}, {"src": "/etc/shadow", "permission": "000", "owner": "root", "owner_group": "root"}]}]}, {"name": "服务器通用基线", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "*", "level": 1, "configs": [{"name": "HIDS进程巡检", "category": "process_exist", "description": "HIDS进程是否正在运行", "args": [{"process_trait": "titanagent"}]}, {"name": "Flume进程巡检", "category": "process_exist", "description": "Flume日志检测进程是否正在运行", "args": [{"process_trait": "flume"}]}, {"name": "node_exporter", "category": "process_exist", "description": "node-exporter巡检", "args": [{"process_trait": "/home/<USER>/node_exporter", "permission": "root"}]}, {"name": "process_exporter", "category": "process_exist", "description": "process_exporter巡检", "args": [{"process_trait": "process-exporter"}]}, {"name": "定时任务配置文件", "category": "path_permission", "description": "目标路径存在且权限一致", "args": [{"src": "/etc/crontab", "permission": "400", "owner": "", "owner_group": ""}]}, {"name": "Flume安装动作检测", "category": "path_permission", "description": "系统管理员是否安装flume", "args": [{"src": "/home/<USER>/flume", "permission": "", "owner": "logadmin", "owner_group": "logadmin"}]}, {"name": "HIDS安装动作检测", "category": "path_permission", "description": "系统管理员是否安装flume", "args": [{"src": "/etc/titanagent/", "permission": "700", "owner": "root", "owner_group": "root"}]}, {"name": "ADDM安装动作检测", "category": "path_permission", "description": "系统管理员是否安装ADDM", "args": [{"src": "/home/<USER>/", "permission": "700", "owner": "ADDMAdmin", "owner_group": "ADDMAdmin"}]}]}, {"name": "redhat_6 /etc通用配置文件对比", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "su配置检查", "category": "path_config_need", "description": "检查su标准基线配置项", "args": [{"src": "/etc/pam.d/su", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/su"}]}, {"name": "sudoers配置检查", "category": "path_config_need", "description": "检查sudoers标准基线配置项", "args": [{"src": "/etc/sudoers", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/sudoers"}]}, {"name": "host.conf配置检查", "category": "path_config_need", "description": "检查host.conf标准基线配置项", "args": [{"src": "/etc/host.conf", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/host.conf"}]}, {"name": "issue配置检查", "category": "path_config_need", "description": "检查issue标准基线配置项", "args": [{"src": "/etc/issue", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/issue"}]}, {"name": "login.defs配置检查", "category": "path_config_need", "description": "检查login.defs标准基线配置项", "args": [{"src": "/etc/login.defs", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/login.defs"}]}, {"name": "sysctl.conf配置检查", "category": "path_config_need", "description": "检查sysctl.conf标准基线配置项", "args": [{"src": "/etc/sysctl.conf", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/sysctl.conf"}]}, {"name": "motd配置检查", "category": "path_config_need", "description": "检查motd标准基线配置项", "args": [{"src": "/etc/motd", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/motd"}]}]}, {"name": "redhat_6 init配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/init/control-alt-delete.conf配置检查", "category": "path_config_need", "description": "检查init标准基线配置项", "args": [{"src": "/etc/init/control-alt-delete.conf", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/init/control-alt-delete.conf"}]}]}, {"name": "redhat_6 audit配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/audit/audit.rules配置检查", "category": "path_config_need", "description": "检查audit.rules标准基线配置项", "args": [{"src": "/etc/audit/audit.rules", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/audit/audit.rules"}]}]}, {"name": "redhat_6 PAM配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/pam.d/su配置检查", "category": "path_config_need", "description": "检查su标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/su", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "category": "path_config_need", "description": "检查su-l标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/su-l", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth配置检查", "category": "path_config_need", "description": "检查system-auth标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/system-auth", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/pam.d/system-auth"}]}]}, {"name": "redhat_6 SECURITY配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/security/limits.conf配置检查", "category": "path_config_need", "description": "检查limits.conf标准基线配置项", "args": [{"src": "/etc/security/limits.conf", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/security/limits.conf"}]}, {"name": "/etc/security/limits.d/90-nproc.conf配置检查", "category": "path_config_need", "description": "检查90-nproc.conf标准基线配置项", "args": [{"src": "/etc/security/limits.d/90-nproc.conf", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/security/limits.d/90-nproc.conf"}]}]}, {"name": "redhat_6 ssh配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "category": "path_config_need", "description": "检查sshd_config标准基线配置项", "args": [{"src": "/etc/ssh/sshd_config", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/ssh/sshd_config"}]}]}, {"name": "redhat_6 sysconfig配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "6", "level": 1, "configs": [{"name": "/etc/sysconfig/selinux配置检查", "category": "path_config_need", "description": "检查selinux标准基线配置项", "args": [{"src": "/etc/sysconfig/selinux", "dest": "/tmp/baseline_check_files/redhat_6/config/default/etc/sysconfig/selinux"}]}]}, {"name": "redhat_7 audit配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/audit/rules.d/access.rules配置检查", "category": "path_config_need", "description": "检查access.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/access.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/access.rule"}]}, {"name": "/etc/audit/rules.d/actions.rules配置检查", "category": "path_config_need", "description": "检查actions.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/actions.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/actions.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules配置检查", "category": "path_config_need", "description": "检查audit_rules_networkconfig_modification.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/audit_rules_networkconfig_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules配置检查", "category": "path_config_need", "description": "检查audit_rules_usergroup_modification.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/audit_rules_usergroup_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_time_rules.rules配置检查", "category": "path_config_need", "description": "检查audit_time_rules.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit_time_rules.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/audit_time_rules.rules"}]}, {"name": "/etc/audit/rules.d/delete.rules配置检查", "category": "path_config_need", "description": "检查delete.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/delete.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/delete.rules"}]}, {"name": "/etc/audit/rules.d/export.rules配置检查", "category": "path_config_need", "description": "检查export.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/export.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/export.rules"}]}, {"name": "/etc/audit/rules.d/immutable.rules配置检查", "category": "path_config_need", "description": "检查immutable.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/immutable.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/immutable.rules"}]}, {"name": "/etc/audit/rules.d/logins.rules配置检查", "category": "path_config_need", "description": "检查logins.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/logins.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/logins.rules"}]}, {"name": "/etc/audit/rules.d/MAC-policy.rules配置检查", "category": "path_config_need", "description": "检查MAC-policy.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/MAC-policy.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/MAC-policy.rules"}]}, {"name": "/etc/audit/rules.d/modules.rules配置检查", "category": "path_config_need", "description": "检查modules.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/modules.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/modules.rules"}]}, {"name": "/etc/audit/rules.d/perm_mod.rules配置检查", "category": "path_config_need", "description": "检查perm_mod.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/perm_mod.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/perm_mod.rules"}]}, {"name": "/etc/audit/rules.d/privileged.rules配置检查", "category": "path_config_need", "description": "检查privileged.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/privileged.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/privileged.rules"}]}, {"name": "/etc/audit/rules.d/session.rules配置检查", "category": "path_config_need", "description": "检查session.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/session.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/session.rules"}]}, {"name": "/etc/audit/rules.d/time-change.rules配置检查", "category": "path_config_need", "description": "检查time-change.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/time-change.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/rules.d/time-change.rules"}]}, {"name": "/etc/audit/audit-stop.rules配置检查", "category": "path_config_need", "description": "检查time-change.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/audit-stop.rules", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/audit/audit-stop.rules"}]}]}, {"name": "redhat_7 PAM配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/pam.d/password-auth-ac配置检查", "category": "path_config_need", "description": "检查access.rules标准基线配置项是否存在", "args": [{"src": "/etc/pam.d/password-auth-ac", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/pam.d/password-auth-ac", "ignore_comments": "True"}]}, {"name": "/etc/pam.d/su配置检查", "category": "path_config_need", "description": "检查su标准基线配置项是否存在", "args": [{"src": "/etc/pam.d/su", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "category": "path_config_need", "description": "检查su-l标准基线配置项是否存在", "args": [{"src": "/etc/pam.d/su-l", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth-ac配置检查", "category": "path_config_need", "description": "检查system-auth-ac标准基线配置项是否存在", "args": [{"src": "/etc/pam.d/system-auth-ac", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/pam.d/system-auth-ac"}]}]}, {"name": "redhat_7 profile.d配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/profile.d/szse.sh配置检查", "category": "path_config_need", "description": "检查access.rules标准基线配置项是否存在", "args": [{"src": "/etc/profile.d/szse.sh", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/profile.d/szse.sh"}]}]}, {"name": "redhat_7 security配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/security/limits.conf配置检查", "category": "path_config_need", "description": "检查limits.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/limits.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/limits.conf"}]}, {"name": "/etc/security/pwquality.conf配置检查", "category": "path_config_need", "description": "检查pwquality.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/pwquality.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/pwquality.conf"}]}, {"name": "/etc/security/limits.d/20-nproc.conf配置检查", "category": "path_config_need", "description": "检查20-nproc.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/limits.d/20-nproc.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/limits.d/20-nproc.conf"}]}]}, {"name": "redhat_7 selinux配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/selinux/config配置检查", "category": "path_config_need", "description": "检查config标准基线配置项是否存在", "args": [{"src": "/etc/selinux/config", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/selinux/config"}]}]}, {"name": "redhat_7 ssh配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "category": "path_config_need", "description": "检查sshd_config标准基线配置项是否存在", "args": [{"src": "/etc/ssh/sshd_config", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/ssh/sshd_config"}]}]}, {"name": "redhat_7 sysctl.d配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/sysctl.d/10-szse.conf配置检查", "category": "path_config_need", "description": "检查sshd_config标准基线配置项是否存在", "args": [{"src": "/etc/sysctl.d/10-szse.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/sysctl.d/10-szse.conf"}]}]}, {"name": "redhat_7 etc配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "7", "level": 1, "configs": [{"name": "/etc/chrony.conf配置检查", "category": "path_config_need", "description": "检查chrony.conf标准基线配置项是否存在", "args": [{"src": "/etc/chrony.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/chrony.conf"}]}, {"name": "/etc/host.conf配置检查", "category": "path_config_need", "description": "检查host.conf标准基线配置项是否存在", "args": [{"src": "/etc/host.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/host.conf"}]}, {"name": "/etc/login.defs配置检查", "category": "path_config_need", "description": "检查login.defs标准基线配置项是否存在", "args": [{"src": "/etc/login.defs", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/login.defs"}]}, {"name": "/etc/sudoers配置检查", "category": "path_config_need", "description": "检查sudoers标准基线配置项是否存在", "args": [{"src": "/etc/sudoers", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/sudoers"}]}]}, {"name": "kylin_10 audit配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/audit/rules.d/access.rules配置检查", "category": "path_config_need", "description": "检查access.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/access.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/access.rule"}]}, {"name": "/etc/audit/rules.d/actions.rules配置检查", "category": "path_config_need", "description": "检查actions.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/actions.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/actions.rules"}]}, {"name": "/etc/audit/rules.d/audit.rules配置检查", "category": "path_config_need", "description": "检查audit.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/audit.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules配置检查", "category": "path_config_need", "description": "检查audit_rules_networkconfig_modification.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/audit_rules_networkconfig_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules配置检查", "category": "path_config_need", "description": "检查audit_rules_usergroup_modification.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/audit_rules_usergroup_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_time_rules.rules配置检查", "category": "path_config_need", "description": "检查audit_time_rules.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/audit_time_rules.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/audit_time_rules.rules"}]}, {"name": "/etc/audit/rules.d/delete.rules配置检查", "category": "path_config_need", "description": "检查delete.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/delete.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/delete.rules"}]}, {"name": "/etc/audit/rules.d/export.rules配置检查", "category": "path_config_need", "description": "检查export.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/export.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/export.rules"}]}, {"name": "/etc/audit/rules.d/immutable.rules配置检查", "category": "path_config_need", "description": "检查immutable.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/immutable.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/immutable.rules"}]}, {"name": "/etc/audit/rules.d/logins.rules配置检查", "category": "path_config_need", "description": "检查logins.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/logins.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/logins.rules"}]}, {"name": "/etc/audit/rules.d/MAC-policy.rules配置检查", "category": "path_config_need", "description": "检查MAC-policy.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/MAC-policy.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/MAC-policy.rules"}]}, {"name": "/etc/audit/rules.d/modules.rules配置检查", "category": "path_config_need", "description": "检查modules.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/modules.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/modules.rules"}]}, {"name": "/etc/audit/rules.d/perm_mod.rules配置检查", "category": "path_config_need", "description": "检查perm_mod.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/perm_mod.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/perm_mod.rules"}]}, {"name": "/etc/audit/rules.d/privileged.rules配置检查", "category": "path_config_need", "description": "检查privileged.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/privileged.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/privileged.rules"}]}, {"name": "/etc/audit/rules.d/session.rules配置检查", "category": "path_config_need", "description": "检查session.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/session.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/session.rules"}]}, {"name": "/etc/audit/rules.d/time-change.rules配置检查", "category": "path_config_need", "description": "检查time-change.rules标准基线配置项是否存在", "args": [{"src": "/etc/audit/rules.d/time-change.rules", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/audit/rules.d/time-change.rules"}]}]}, {"name": "kylin_10 PAM配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/pam.d/password-auth配置检查", "category": "path_config_need", "description": "检查password-auth标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/password-auth", "dest": "./kylin_v10/config/default/etc/pam.d/password-auth", "ignore_comments": "True"}]}, {"name": "/etc/pam.d/su配置检查", "category": "path_config_need", "description": "检查su标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/su", "dest": "./kylin_v10/config/default/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "category": "path_config_need", "description": "检查su-l标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/su-l", "dest": "./kylin_v10/config/default/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth配置检查", "category": "path_config_need", "description": "检查system-auth标准基线配置项存在与否", "args": [{"src": "/etc/pam.d/system-auth", "dest": "./kylin_v10/config/default/etc/pam.d/system-auth"}]}]}, {"name": "kylin_10 profile.d配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/profile.d/szse.sh配置检查", "category": "path_config_need", "description": "检查access.rules标准基线配置项是否存在", "args": [{"src": "/etc/profile.d/szse.sh", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/profile.d/szse.sh"}]}]}, {"name": "kylin_10 security配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/security/limits.conf配置检查", "category": "path_config_need", "description": "检查limits.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/limits.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/limits.conf"}]}, {"name": "/etc/security/pwquality.conf配置检查", "category": "path_config_need", "description": "检查pwquality.conf标准基线配置项是否存在", "args": [{"src": "/etc/security/pwquality.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/security/pwquality.conf"}]}]}, {"name": "kylin_10 selinux配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/selinux/config配置检查", "category": "path_config_need", "description": "检查config标准基线配置项是否存在", "args": [{"src": "/etc/selinux/config", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/selinux/config"}]}]}, {"name": "kylin_10 ssh配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "category": "path_config_need", "description": "检查sshd_config标准基线配置项是否存在", "args": [{"src": "/etc/ssh/sshd_config", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/ssh/sshd_config"}]}]}, {"name": "kylin_10 etc配置文件检查", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "10", "level": 1, "configs": [{"name": "/etc/chrony.conf配置检查", "category": "path_config_need", "description": "检查chrony.conf标准基线配置项是否存在", "args": [{"src": "/etc/chrony.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/chrony.conf"}]}, {"name": "/etc/host.conf配置检查", "category": "path_config_need", "description": "检查host.conf标准基线配置项是否存在", "args": [{"src": "/etc/host.conf", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/host.conf"}]}, {"name": "/etc/login.defs配置检查", "category": "path_config_need", "description": "检查login.defs标准基线配置项是否存在", "args": [{"src": "/etc/login.defs", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/login.defs"}]}, {"name": "/etc/sudoers配置检查", "category": "path_config_need", "description": "检查sudoers标准基线配置项是否存在", "args": [{"src": "/etc/sudoers", "dest": "/tmp/baseline_check_files/redhat_7/config/default/etc/sudoers"}]}, {"name": "/etc/sysctl.conf配置检查", "category": "path_config_need", "description": "检查sysctl.conf标准基线配置项是否存在", "args": [{"src": "/etc/sysctl.conf", "dest": "/tmp/baseline_check_files/kylin_v10/config/default/etc/sysctl.conf"}]}]}]