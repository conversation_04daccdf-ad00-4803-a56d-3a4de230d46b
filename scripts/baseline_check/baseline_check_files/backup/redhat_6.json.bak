[{"event": "redhat_6 主机名IP映射检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "主机名IP映射检查", "description": "hostname和IP列表精准匹配", "checker_type": "hostname_check", "args": [{}]}]}, {"event": "密码审计基线", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "配置文件检测", "description": "目标文件路径需要同源文件路径一致", "checker_type": "path_compare", "args": [{"dest": "/etc/pam.d/su", "src": "./template/redhat_6/config/default/etc/pam.d/su"}, {"dest": "/etc/pam.d/su-l", "src": "./template/redhat_6/config/default/etc/pam.d/su-l"}, {"dest": "/etc/pam.d/system-auth", "src": "./template/redhat_6/config/default/etc/pam.d/system-auth"}]}, {"name": "密码策略文件权限检查", "description": "目标路径存在且权限一致", "checker_type": "path_permission", "args": [{"owner": "root", "src": "/etc/passwd", "owner_group": "root", "permission": "644"}, {"owner": "root", "src": "/etc/shadow", "owner_group": "root", "permission": "000"}]}]}, {"event": "服务器通用基线", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "HIDS进程巡检", "description": "HIDS进程是否正在运行", "checker_type": "process_check", "args": [{"process_trait": "titanagent"}]}, {"name": "Flume进程巡检", "description": "Flume日志检测进程是否正在运行", "checker_type": "process_check", "args": [{"process_trait": "flume"}]}, {"name": "node_exporter", "description": "node-exporter巡检", "checker_type": "process_check", "args": [{"process_trait": "/home/<USER>/node_exporter", "permission": "root"}]}, {"name": "process_exporter", "description": "process_exporter巡检", "checker_type": "process_check", "args": [{"process_trait": "process-exporter"}]}, {"name": "定时任务配置文件", "description": "目标路径存在且权限一致", "checker_type": "path_permission", "args": [{"owner": "", "src": "/etc/crontab", "owner_group": "", "permission": "400"}]}, {"name": "Flume安装动作检测", "description": "系统管理员是否安装flume", "checker_type": "path_permission", "args": [{"owner": "logadmin", "src": "/home/<USER>/flume", "owner_group": "logadmin", "permission": ""}]}, {"name": "HIDS安装动作检测", "description": "系统管理员是否安装flume", "checker_type": "path_permission", "args": [{"owner": "root", "src": "/etc/titanagent/", "owner_group": "root", "permission": "700"}]}, {"name": "ADDM安装动作检测", "description": "系统管理员是否安装ADDM", "checker_type": "path_permission", "args": [{"owner": "ADDMAdmin", "src": "/home/<USER>/", "owner_group": "ADDMAdmin", "permission": "700"}]}]}, {"event": "redhat_6 /etc通用配置文件对比", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "su配置检查", "description": "检查su标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/pam.d/su", "src": "/etc/pam.d/su"}]}, {"name": "sudoers配置检查", "description": "检查sudoers标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/sudoers", "src": "/etc/sudoers"}]}, {"name": "host.conf配置检查", "description": "检查host.conf标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/host.conf", "src": "/etc/host.conf"}]}, {"name": "issue配置检查", "description": "检查issue标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/issue", "src": "/etc/issue"}]}, {"name": "login.defs配置检查", "description": "检查login.defs标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/login.defs", "src": "/etc/login.defs"}]}, {"name": "sysctl.conf配置检查", "description": "检查sysctl.conf标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/sysctl.conf", "src": "/etc/sysctl.conf"}]}, {"name": "motd配置检查", "description": "检查motd标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/motd", "src": "/etc/motd"}]}]}, {"event": "redhat_6 init配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/init/control-alt-delete.conf配置检查", "description": "检查init标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/init/control-alt-delete.conf", "src": "/etc/init/control-alt-delete.conf"}]}]}, {"event": "redhat_6 audit配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/audit/audit.rules配置检查", "description": "检查audit.rules标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/audit/audit.rules", "src": "/etc/audit/audit.rules"}]}]}, {"event": "redhat_6 PAM配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/pam.d/su配置检查", "description": "检查su标准基线配置项存在与否", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/pam.d/su", "src": "/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "description": "检查su-l标准基线配置项存在与否", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/pam.d/su-l", "src": "/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth配置检查", "description": "检查system-auth标准基线配置项存在与否", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/pam.d/system-auth", "src": "/etc/pam.d/system-auth"}]}]}, {"event": "redhat_6 SECURITY配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/security/limits.conf配置检查", "description": "检查limits.conf标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/security/limits.conf", "src": "/etc/security/limits.conf"}]}, {"name": "/etc/security/limits.d/90-nproc.conf配置检查", "description": "检查90-nproc.conf标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/security/limits.d/90-nproc.conf", "src": "/etc/security/limits.d/90-nproc.conf"}]}]}, {"event": "redhat_6 ssh配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "description": "检查sshd_config标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/ssh/sshd_config", "src": "/etc/ssh/sshd_config"}]}]}, {"event": "redhat_6 sysconfig配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/sysconfig/selinux配置检查", "description": "检查selinux标准基线配置项", "checker_type": "path_config_need", "args": [{"dest": "./template/redhat_6/config/default/etc/sysconfig/selinux", "src": "/etc/sysconfig/selinux"}]}]}]