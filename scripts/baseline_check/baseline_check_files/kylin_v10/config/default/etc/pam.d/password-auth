#auth       required       pam_kysec.so
#%PAM-1.0
# User changes will be destroyed the next time authconfig is run.
auth        required      pam_env.so

# SZSE CHANGE BEGIN
#auth        required      pam_faillock.so preauth audit deny=3 even_deny_root unlock_time=60
auth        required      pam_faillock.so preauth audit deny=12 unlock_time=300 even_deny_root unlock_time=200 fail_interval=900
# SZSE CHANGE END
-auth        sufficient    pam_fprintd.so
auth        sufficient    pam_unix.so nullok try_first_pass
-auth        sufficient    pam_sss.so use_first_pass
# SZSE CHANGE BEGIN
#auth        [default=die] pam_faillock.so authfail audit deny=3 even_deny_root unlock_time=60
auth        [default=die] pam_faillock.so authfail audit deny=12 unlock_time=300 even_deny_root unlock_time=200 fail_interval=900
#auth        sufficient    pam_faillock.so authsucc audit deny=3 even_deny_root unlock_time=60
auth        sufficient    pam_faillock.so authsucc audit deny=12 unlock_time=300 even_deny_root unlock_time=200 fail_interval=900
# SZSE CHANGE END
auth        requisite     pam_succeed_if.so uid >= 1000 quiet_success
auth        required      pam_deny.so

# SZSE CHANGE BEGIN
account     required      pam_faillock.so
# SZSE CHANGE END
account     required      pam_unix.so
account     sufficient    pam_localuser.so
account     sufficient    pam_succeed_if.so uid < 1000 quiet
-account     [default=bad success=ok user_unknown=ignore] pam_sss.so
account     required      pam_permit.so

password    requisite     pam_pwquality.so try_first_pass local_users_only
password    sufficient    pam_unix.so sha512 shadow nullok try_first_pass use_authtok
-password    sufficient    pam_sss.so use_authtok
password    required      pam_deny.so

session     optional      pam_keyinit.so revoke
session     required      pam_limits.so
-session     optional      pam_systemd.so
session     [success=1 default=ignore] pam_succeed_if.so service in crond quiet use_uid
session     required      pam_unix.so
-session     optional      pam_sss.so
