#!/bin/bash
run_time=$(date "+%Y-%m-%d_%H-%M-%S")
rm -f /etc/fstab_bak*
cp /etc/fstab /etc/fstab_bak_${run_time}
while read line
do
   [ -z "$line" ] && continue

   if echo "$line" | grep "^#" > /dev/null 2>&1; then
      echo "$line" >>/etc/fstab_tmp
      continue
   fi

   echo $line|awk '{print $2}'|grep -E "/var|/tmp" >/dev/null
   flag=$?
   if [ ${flag} -eq 0 ]; then
      echo "`echo $line|awk '{print $1,$2,$3}'`  defaults,nosuid,nodev `echo $line |awk  '{print $5,$6}'`" >>/etc/fstab_tmp
   else
      echo $line >>/etc/fstab_tmp
   fi

done < /etc/fstab

mv -f /etc/fstab_tmp /etc/fstab
