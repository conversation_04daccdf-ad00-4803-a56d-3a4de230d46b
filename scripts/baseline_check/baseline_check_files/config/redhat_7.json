[{"event": "redhat_7 主机名IP映射检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "主机名IP映射检查", "description": "hostname和IP列表精准匹配", "checker_type": "hostname_checker", "args": [{}]}]}, {"event": "redhat_7 rc.local服务状态检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "rc.local服务状态检查", "description": "rc.local服务状态检查", "checker_type": "shell_checker", "args": [{"script": "systemctl status rc-local", "expect": "Active: active (exited)"}]}]}, {"event": "密码审计基线", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "配置文件检测", "description": "目标文件路径需要同源文件路径一致", "checker_type": "file_checker", "args": [{"pattern": "exact", "target": "/etc/pam.d/su", "template": "./template/redhat_7/config/default/etc/pam.d/su"}, {"pattern": "exact", "target": "/etc/pam.d/su-l", "template": "./template/redhat_7/config/default/etc/pam.d/su-l"}, {"pattern": "exact", "target": "/etc/pam.d/password-auth-ac", "template": "./template/redhat_7/config/default/etc/pam.d/password-auth-ac"}, {"pattern": "exact", "target": "/etc/pam.d/system-auth-ac", "template": "./template/redhat_7/config/default/etc/pam.d/system-auth-ac"}, {"pattern": "exact", "target": "/etc/security/pwquality.conf", "template": "./template/redhat_7/config/default/etc/security/pwquality.conf"}]}, {"name": "密码策略文件权限检查", "description": "目标路径存在且权限一致", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/etc/passwd", "owner": "root", "owner_group": "root", "permission": "644"}, {"pattern": "permission", "target": "/etc/shadow", "owner": "root", "owner_group": "root", "permission": "0"}]}]}, {"event": "redhat_7 audit配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/audit/rules.d/access.rules配置检查", "description": "检查access.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/access.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/access.rules"}]}, {"name": "/etc/audit/rules.d/actions.rules配置检查", "description": "检查actions.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/actions.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/actions.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules配置检查", "description": "检查audit_rules_networkconfig_modification.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/audit_rules_networkconfig_modification.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/audit_rules_networkconfig_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules配置检查", "description": "检查audit_rules_usergroup_modification.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/audit_rules_usergroup_modification.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/audit_rules_usergroup_modification.rules"}]}, {"name": "/etc/audit/rules.d/audit_time_rules.rules配置检查", "description": "检查audit_time_rules.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/audit_time_rules.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/audit_time_rules.rules"}]}, {"name": "/etc/audit/rules.d/delete.rules配置检查", "description": "检查delete.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/delete.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/delete.rules"}]}, {"name": "/etc/audit/rules.d/export.rules配置检查", "description": "检查export.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/export.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/export.rules"}]}, {"name": "/etc/audit/rules.d/immutable.rules配置检查", "description": "检查immutable.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/immutable.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/immutable.rules"}]}, {"name": "/etc/audit/rules.d/logins.rules配置检查", "description": "检查logins.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/logins.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/logins.rules"}]}, {"name": "/etc/audit/rules.d/MAC-policy.rules配置检查", "description": "检查MAC-policy.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/MAC-policy.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/MAC-policy.rules"}]}, {"name": "/etc/audit/rules.d/modules.rules配置检查", "description": "检查modules.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/modules.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/modules.rules"}]}, {"name": "/etc/audit/rules.d/perm_mod.rules配置检查", "description": "检查perm_mod.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/perm_mod.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/perm_mod.rules"}]}, {"name": "/etc/audit/rules.d/privileged.rules配置检查", "description": "检查privileged.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/privileged.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/privileged.rules"}]}, {"name": "/etc/audit/rules.d/session.rules配置检查", "description": "检查session.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/session.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/session.rules"}]}, {"name": "/etc/audit/rules.d/time-change.rules配置检查", "description": "检查time-change.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/rules.d/time-change.rules", "template": "./template/redhat_7/config/default/etc/audit/rules.d/time-change.rules"}]}, {"name": "/etc/audit/audit-stop.rules配置检查", "description": "检查time-change.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/audit-stop.rules", "template": "./template/redhat_7/config/default/etc/audit/audit-stop.rules"}]}]}, {"event": "redhat_7 PAM配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/pam.d/password-auth-ac配置检查", "description": "检查access.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/password-auth-ac", "template": "./template/redhat_7/config/default/etc/pam.d/password-auth-ac"}]}, {"name": "/etc/pam.d/su配置检查", "description": "检查su标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/su", "template": "./template/redhat_7/config/default/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "description": "检查su-l标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/su-l", "template": "./template/redhat_7/config/default/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth-ac配置检查", "description": "检查system-auth-ac标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/system-auth-ac", "template": "./template/redhat_7/config/default/etc/pam.d/system-auth-ac"}]}]}, {"event": "redhat_7 profile.d配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/profile.d/szse.sh配置检查", "description": "检查access.rules标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/profile.d/szse.sh", "template": "./template/redhat_7/config/default/etc/profile.d/szse.sh"}]}]}, {"event": "redhat_7 security配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/security/limits.conf配置检查", "description": "检查limits.conf标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/security/limits.conf", "template": "./template/redhat_7/config/default/etc/security/limits.conf"}]}, {"name": "/etc/security/pwquality.conf配置检查", "description": "检查pwquality.conf标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/security/pwquality.conf", "template": "./template/redhat_7/config/default/etc/security/pwquality.conf"}]}, {"name": "/etc/security/limits.d/20-nproc.conf配置检查", "description": "检查20-nproc.conf标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/security/limits.d/20-nproc.conf", "template": "./template/redhat_7/config/default/etc/security/limits.d/20-nproc.conf"}]}]}, {"event": "redhat_7 selinux配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/selinux/config配置检查", "description": "检查config标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/selinux/config", "template": "./template/redhat_7/config/default/etc/selinux/config"}]}]}, {"event": "redhat_7 ssh配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "description": "检查sshd_config标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/ssh/sshd_config", "template": "./template/redhat_7/config/default/etc/ssh/sshd_config"}]}]}, {"event": "redhat_7 sysctl.d配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/sysctl.d/10-szse.conf配置检查", "description": "检查sshd_config标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/sysctl.d/10-szse.conf", "template": "./template/redhat_7/config/default/etc/sysctl.d/10-szse.conf"}]}]}, {"event": "redhat_7 etc配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/chrony.conf配置检查", "description": "检查chrony.conf标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/chrony.conf", "template": "./template/redhat_7/config/default/etc/chrony.conf"}]}, {"name": "/etc/host.conf配置检查", "description": "检查host.conf标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/host.conf", "template": "./template/redhat_7/config/default/etc/host.conf"}]}, {"name": "/etc/login.defs配置检查", "description": "检查login.defs标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/login.defs", "template": "./template/redhat_7/config/default/etc/login.defs"}]}, {"name": "/etc/sudoers配置检查", "description": "检查sudoers标准基线配置项是否存在", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/sudoers", "template": "./template/redhat_7/config/default/etc/sudoers"}]}]}]