[{"event": "主DNS服务器配置检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "主DNS服务器配置检查", "description": "主DNS服务器配置检查", "checker_type": "dns_checker", "args": [{}]}]}, {"event": "服务器通用启动脚本检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "rc.local文件权限检查", "description": "rc.local文件权限检查", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/etc/rc.local", "owner": "root", "owner_group": "root", "permission": "777"}, {"pattern": "permission", "target": "/etc/rc.d/rc.local", "owner": "root", "owner_group": "root", "permission": "755"}]}]}, {"event": "general-purpose baseline", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "HIDS进程巡检", "description": "HIDS进程是否正在运行", "checker_type": "process_checker", "args": [{"process_trait": "titanagent"}]}, {"name": "Flume进程巡检", "description": "Flume日志检测进程是否正在运行", "checker_type": "process_checker", "args": [{"process_trait": "flume"}]}, {"name": "node_exporter", "description": "node-exporter巡检", "checker_type": "process_checker", "args": [{"process_trait": "/home/<USER>/node_exporter", "permission": "root"}]}, {"name": "process_exporter", "description": "process_exporter巡检", "checker_type": "process_checker", "args": [{"process_trait": "process-exporter"}]}, {"name": "定时任务配置文件", "description": "目标路径存在且权限一致", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/etc/crontab", "owner": "", "owner_group": "", "permission": "400"}]}, {"name": "Flume安装动作检测", "description": "系统管理员是否安装flume", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/home/<USER>/flume", "owner": "logadmin", "owner_group": "logadmin", "permission": ""}]}, {"name": "HIDS安装动作检测", "description": "系统管理员是否安装flume", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/etc/titanagent/", "owner": "root", "owner_group": "root", "permission": "700"}]}, {"name": "ADDM安装动作检测", "description": "系统管理员是否安装ADDM", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/home/<USER>/", "owner": "ADDMAdmin", "owner_group": "ADDMAdmin", "permission": "700"}]}]}]