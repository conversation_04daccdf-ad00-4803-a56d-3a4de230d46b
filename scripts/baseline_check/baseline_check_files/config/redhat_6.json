[{"event": "redhat_6 主机名IP映射检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "主机名IP映射检查", "description": "hostname和IP列表精准匹配", "checker_type": "hostname_checker", "args": [{}]}]}, {"event": "密码审计基线", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "配置文件检测", "description": "目标文件路径需要同源文件路径一致", "checker_type": "file_checker", "args": [{"pattern": "exact", "target": "/etc/pam.d/su", "template": "./template/redhat_6/config/default/etc/pam.d/su"}, {"pattern": "exact", "target": "/etc/pam.d/su-l", "template": "./template/redhat_6/config/default/etc/pam.d/su-l"}, {"pattern": "exact", "target": "/etc/pam.d/system-auth", "template": "./template/redhat_6/config/default/etc/pam.d/system-auth"}]}, {"name": "密码策略文件权限检查", "description": "目标路径存在且权限一致", "checker_type": "file_checker", "args": [{"pattern": "permission", "target": "/etc/passwd", "owner": "root", "owner_group": "root", "permission": "644"}, {"pattern": "permission", "target": "/etc/shadow", "owner": "root", "owner_group": "root", "permission": "0"}]}]}, {"event": "redhat_6 /etc通用配置文件对比", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "su配置检查", "description": "检查su标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/su", "template": "./template/redhat_6/config/default/etc/pam.d/su"}]}, {"name": "sudoers配置检查", "description": "检查sudoers标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/sudoers", "template": "./template/redhat_6/config/default/etc/sudoers"}]}, {"name": "host.conf配置检查", "description": "检查host.conf标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/host.conf", "template": "./template/redhat_6/config/default/etc/host.conf"}]}, {"name": "issue配置检查", "description": "检查issue标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/issue", "template": "./template/redhat_6/config/default/etc/issue"}]}, {"name": "login.defs配置检查", "description": "检查login.defs标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/login.defs", "template": "./template/redhat_6/config/default/etc/login.defs"}]}, {"name": "sysctl.conf配置检查", "description": "检查sysctl.conf标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/sysctl.conf", "template": "./template/redhat_6/config/default/etc/sysctl.conf"}]}, {"name": "motd配置检查", "description": "检查motd标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/motd", "template": "./template/redhat_6/config/default/etc/motd"}]}]}, {"event": "redhat_6 init配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/init/control-alt-delete.conf配置检查", "description": "检查init标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/init/control-alt-delete.conf", "template": "./template/redhat_6/config/default/etc/init/control-alt-delete.conf"}]}]}, {"event": "redhat_6 audit配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/audit/audit.rules配置检查", "description": "检查audit.rules标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/audit/audit.rules", "template": "./template/redhat_6/config/default/etc/audit/audit.rules"}]}]}, {"event": "redhat_6 PAM配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/pam.d/su配置检查", "description": "检查su标准基线配置项存在与否", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/su", "template": "./template/redhat_6/config/default/etc/pam.d/su"}]}, {"name": "/etc/pam.d/su-l配置检查", "description": "检查su-l标准基线配置项存在与否", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/su-l", "template": "./template/redhat_6/config/default/etc/pam.d/su-l"}]}, {"name": "/etc/pam.d/system-auth配置检查", "description": "检查system-auth标准基线配置项存在与否", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/pam.d/system-auth", "template": "./template/redhat_6/config/default/etc/pam.d/system-auth"}]}]}, {"event": "redhat_6 SECURITY配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/security/limits.conf配置检查", "description": "检查limits.conf标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/security/limits.conf", "template": "./template/redhat_6/config/default/etc/security/limits.conf"}]}, {"name": "/etc/security/limits.d/90-nproc.conf配置检查", "description": "检查90-nproc.conf标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/security/limits.d/90-nproc.conf", "template": "./template/redhat_6/config/default/etc/security/limits.d/90-nproc.conf"}]}]}, {"event": "redhat_6 ssh配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/ssh/sshd_config配置检查", "description": "检查sshd_config标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/ssh/sshd_config", "template": "./template/redhat_6/config/default/etc/ssh/sshd_config"}]}]}, {"event": "redhat_6 sysconfig配置文件检查", "level": 1, "date": "2025/03/01", "regex": {"ip": ".*", "hostname": ".*", "arch": ".*"}, "configs": [{"name": "/etc/sysconfig/selinux配置检查", "description": "检查selinux标准基线配置项", "checker_type": "file_checker", "args": [{"pattern": "include", "target": "/etc/sysconfig/selinux", "template": "./template/redhat_6/config/default/etc/sysconfig/selinux"}]}]}]