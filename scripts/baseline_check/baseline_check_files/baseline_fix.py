#!/usr/bin/python
# coding=utf-8
import argparse, platform

from baseline_config import BaselineConfigLoader
#from scripts.baseline_check.baseline_check_files.baseline_fix_plugins import *
from baseline_fix_plugins import *

KYLIN10_BASELINE_PATH = './config/kylin_v10.json'
RHEL6_BASELINE_PATH = './config/redhat_6.json'
RHEL7_BASELINE_PATH = './config/redhat_7.json'
GENERAL_BASELINE_PATH = './config/general.json'

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='baseline fix script')
    parser.add_argument('-c', '--check_json', type=str, default=None, help='配置文件路径')
    parser.add_argument('-k', '--key', type=str, default='', help='巡检事项关键字')
    args = parser.parse_args()

    if args.check_json:
        specific_baseline_loader = BaselineConfigLoader(args.check_json)
    else:
        #load baseline config based on os version
        if platform.dist()[1]:
            cur_os = platform.dist()[1][0]
        elif 'ky10' in platform.platform():
            cur_os = '1'

        if cur_os == '6':
            specific_baseline_loader = BaselineConfigLoader(RHEL6_BASELINE_PATH)
        if cur_os == '7':
            specific_baseline_loader = BaselineConfigLoader(RHEL7_BASELINE_PATH)
        if cur_os == '1':
            specific_baseline_loader = BaselineConfigLoader(KYLIN10_BASELINE_PATH)

    specific_baseline_configs = specific_baseline_loader.load()
    general_baseline_loader = BaselineConfigLoader(GENERAL_BASELINE_PATH)
    general_baseline_configs = general_baseline_loader.load()
    #list of all BaselineEvent to be checked
    baseline_configs = general_baseline_configs + specific_baseline_configs

    fixer = Fixer(args.key)
    fixer.register(FileFixPlugin())

    fixer.fix_all(baseline_configs)    
    fixer.export_results()