#!/usr/bin/python
# coding=utf-8
import json
import os
import platform
import re
import socket
import subprocess

from baseline_results import *

BASELINE_RESULT = 'result.json'

class CheckerPlugin:
    name = 'defalut_checker'

    def __init__(self):
        pass

    def check(self, baseline_config_item):
        print('check')
        if self.name != baseline_config_item.checker_type:
            return None

class ProcessCheckPlugin(CheckerPlugin):
    name = 'process_checker'

    def check(self, baseline_config_item):
        if self.name != baseline_config_item.checker_type:
            return None
        config_result = BaselineConfigItemResult(baseline_config_item)
        for arg in baseline_config_item.args:
            process_trait = arg.get("process_trait")
            permission = arg.get("permission")
            ans, desc = self.check_process(process_trait, permission)            
            args_result = BaselineConfigItemArgsResult(arg, ans, desc)
            config_result.args_result.append(args_result)
            
        return config_result

    def check_process(self, process_trait, permission):

        if not process_trait:
            return False, '缺少process_trait配置'

        ps = subprocess.Popen(
            args=['ps', '-ef'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        grep = subprocess.Popen(
            args=['grep', process_trait],
            stdin=ps.stdout, stdout=subprocess.PIPE
        )
        grep = subprocess.Popen(
            args=['grep', '-v', 'grep'],
            stdin=grep.stdout, stdout=subprocess.PIPE
        )
        ans = grep.stdout.readlines()

        #process doesn't exist
        if not ans:
            return False, '{0}进程不存在'.format(process_trait)
        
        #process permission check
        if permission:
            error_list = []
            """
            root      99258      1  0  2024 ?        00:00:00 nginx: master process ./nginx
            nobody    99260  99258  0  2024 ?        00:00:00 nginx: worker process
            """
            for item in ans:
                actual_permission = item.split()[0]
                pid = item.split()[1]
                if actual_permission != permission:
                    error = '当前{0}进程[{1}]为{2}用户权限, 期待用户权限为{3}'.format(process_trait, pid, actual_permission, permission)
                    error_list.append(error)
            if error_list:
                return False, ''.join(error_list)
            
        return True, ''.join(ans)

class FileCheckPlugin(CheckerPlugin):
    name = 'file_checker'

    def check(self, baseline_config_item):
        if self.name != baseline_config_item.checker_type:
            return None
        config_result = BaselineConfigItemResult(baseline_config_item)
        for arg in baseline_config_item.args:
            pattern = arg.get("pattern")
            target = arg.get("target")
            template = arg.get("template")            
            permission = arg.get('permission')
            owner = arg.get('owner')
            owner_group = arg.get('owner_group')

            if pattern == 'include':
                ans, desc = self.check_inclusion(target, template)
            elif pattern == 'exact':
                ans, desc = self.check_exact_match(target, template)
            elif pattern == 'exist':
                ans, desc = self.check_existence(target)
            elif pattern == 'permission':
                ans, desc = self.check_permission(target, permission, owner, owner_group)
            else:
                print('unrecognized file_checker pattern {0}'.format(pattern))
                return None
            
            args_result = BaselineConfigItemArgsResult(arg, ans, desc)
            config_result.args_result.append(args_result)
            
        return config_result

    def check_inclusion(self, target, template):
        # p1 = subprocess.Popen(
        #     args=['grep', '-Fxvf', target, template],
        #     stdout=subprocess.PIPE, 
        #     stderr=subprocess.PIPE
        # )

        # p2 = subprocess.Popen(
        #     args=['grep', '-v', '^#'], 
        #     stdin=p1.stdout, 
        #     stdout=subprocess.PIPE, 
        #     stderr=subprocess.PIPE
        # )
        
        # p1_stderr = p1.stderr.read()
        # p2_stdout, p2_stderr = p2.communicate()        
        # err = p1_stderr + p2_stderr

        # p1.stdout.close()
        # p1.stderr.close()
        
        # p2.stdout.close()
        # p2.stderr.close()

        # ans = True
        # des = ''
        # if p2_stdout:
        #     ans = False
        #     des += '相比标准基线缺少:\n{0}\n'.format(p2_stdout)            
        # if err:
        #     ans = False
        #     des += '比对过程中错误:\n{0}'.format(err)

        # return ans, des

        ans = True
        des = ''
        configs_to_match = []
        with open(template, 'r') as tem:
            for line in tem.readlines():
                line = line.strip()
                if line and not line.startswith('#'):
                    #normalize: replace tab/multiple spaces with one single space
                    line = ' '.join(line.split())
                    configs_to_match.append(line.strip())

        try:
            with open(target, 'r') as tar:
                for line in tar.readlines():
                    line = line.strip()
                    if line and not line.startswith('#'):
                        #normalize: replace tab/multiple spaces with one single space
                        line = ' '.join(line.split())
                        if line in configs_to_match:
                            configs_to_match.remove(line)
        except IOError:
            ans = False
            des = '没有找到文件或读取失败'
        else:
            if len(configs_to_match):
                ans = False
                des = '相比标准基线缺少:\n{0}\n'.format(' '.join(configs_to_match))
        
        return ans, des

    def check_exact_match(self, target, template):
        p1 = subprocess.Popen(
            args=['diff', '-bBupr', template, target, '--ignore-matching-lines=^#'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()

        ans = True
        des = ''
        if stdout:
            ans = False
            des += '文本不完全相同，比对结果:\n{0}\n'.format(stdout)
        if stderr:
            ans = False
            des += '比对过程中错误:\n{0}'.format(stderr)

        return ans, des

    def check_existence(self, target):
        p1 = subprocess.Popen(
            args=['stat', target],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()

        if stderr:
            ans = False
            des = '文件不存在:\n{0}'.format(stderr)
        if stdout:
            ans = True
            des = ''
        
        return ans, des

    def check_permission(self, target, permission, owner, owner_group):
        ans = True
        des = ''
        if not permission and not owner and not owner_group:
            ans = False
            des = '必须配置至少一项权限检查参数[permission, owner, owner_group]'

        if permission:
            out, err = self.stat_func('%a', target)            
            if err:
                ans = False
                des += '权限检查错误：\n{0}\n'.format(err)
            elif out != permission:
                ans = False
                des += '当前权限{0}，期望权限{1}\n'.format(out, permission)
        
        if owner:
            out, err = self.stat_func('%U', target)            
            if err:
                ans = False
                des += 'owner检查错误：\n{0}\n'.format(err)
            elif out != owner:
                ans = False
                des += '当前owner{0}，期望owner{1}\n'.format(out, owner)

        if owner_group:
            out, err = self.stat_func('%G', target)
            if err:
                ans = False
                des += 'group检查错误：\n{0}'.format(err)
            elif out != owner_group:
                ans = False
                des += '当前group{0}，期望group{1}'.format(out, owner_group)

        return ans, des
    
    def stat_func(self, option, target):
        p1 = subprocess.Popen(
            args=['stat', '-c', option, target],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()        
        p1.stdout.close()
        p1.stderr.close()

        return stdout.strip(), stderr.strip()

class ShellScriptPlugin(CheckerPlugin):
    name = 'shell_checker'

    def __init__(self):
        CheckerPlugin.__init__(self)

    def check_shell(self, script, expect):
        script = script.split(' ')
        p1 = subprocess.Popen(
            args=script,
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()

        if expect in stdout:
            return True, ''
        else:
            return False, stdout + stderr

    def check(self, baseline_config_item):
        if self.name != baseline_config_item.checker_type:
            return None
        config_result = BaselineConfigItemResult(baseline_config_item)
        for arg in baseline_config_item.args:
            script = arg.get("script")
            expect = arg.get("expect")
            ans, desc = self.check_shell(script, expect)

            args_result = BaselineConfigItemArgsResult(arg, ans, desc)
            config_result.args_result.append(args_result)
            
        return config_result

class HostnameCheckPlugin(CheckerPlugin):
    """
    主机名IP映射检查
    """
    name = 'hostname_checker'

    def __init__(self):
        CheckerPlugin.__init__(self)
        self.hostname_dict={}
        with open('./config/hostname.json', 'r+') as f:
            self.hostname_dict = json.load(f)

    def check_hostname(self):
        #get hostname
        p1 = subprocess.Popen(
            args=['hostname'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        hostname, stderr1 = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()
        hostname = hostname.strip()

        #only check the mapping if the hostname is included in the excel sheet
        if hostname in self.hostname_dict.keys():
            expected_ips = self.hostname_dict[hostname]
            expected_ips = set(expected_ips.split(','))

            p2 = subprocess.Popen(
                args=['hostname', '-I'], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE
            )

            actual_ips, stderr2 = p2.communicate()            
            actual_ips = set(actual_ips.strip().split(' '))
            p2.stdout.close()
            p2.stderr.close()

            description = '主机名:{0} 期望IP:{1} 实际IP:{2}'.format(hostname, ','.join(list(expected_ips)), ','.join(list(actual_ips)))

            if actual_ips >= expected_ips:
                return True, description
            else:
                return False, description
        else:
            return True, ''

    def check(self, baseline_config_item):
        if self.name != baseline_config_item.checker_type:
            return None
        config_result = BaselineConfigItemResult(baseline_config_item)
        for arg in baseline_config_item.args:
            ans, desc = self.check_hostname()

            args_result = BaselineConfigItemArgsResult(arg, ans, desc)
            config_result.args_result.append(args_result)
            
        return config_result

class DNSPlugin(CheckerPlugin):
    name = 'dns_checker'

    def get_master_nameserver(self, config):
        for line in config.split('\n'):
            if line.startswith('nameserver'):
                return line.split('nameserver')[-1].strip()
        
        return None

    def check_dns(self):
        p1 = subprocess.Popen(
            args=['cat', '/etc/resolv.conf'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = p1.communicate()
        p1.stdout.close()
        p1.stderr.close()

        master_nameserver = self.get_master_nameserver(stdout)

        if master_nameserver != '************' and master_nameserver != '************':
            return False, '/etc/resolv.conf文件内容:\n {0}'.format(stdout)
        
        return True, ''

    def check(self, baseline_config_item):
        if self.name != baseline_config_item.checker_type:
            return None

        config_result = BaselineConfigItemResult(baseline_config_item)
        for arg in baseline_config_item.args:
            ans, desc = self.check_dns()
            args_result = BaselineConfigItemArgsResult(arg, ans, desc)
            config_result.args_result.append(args_result)
            
        return config_result

class Checker:
    def __init__(self, event_keyword = ''):
        self.event_keyword = event_keyword
        self.results = []
        self.plugins = {}
        self.ip = self.get_ip()
        self.hostname = socket.gethostname()
        self.system_platform = ' '.join(platform.dist()) + ' (' + platform.uname()[2] + ')'
        self.arch = platform.uname()[-1]

    def register(self, checker_plugin):
        if checker_plugin and isinstance(checker_plugin, CheckerPlugin):
            self.plugins[checker_plugin.name] = checker_plugin
        else:
            print('plugin {0} registration failed'.format(checker_plugin.name))

    def get_ip(self):
        stdout, stderr = subprocess.Popen(
            args=['hostname', '-I'],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
        ).communicate()
        return stdout.strip()

    def match(self, baseline_config):
        """根据基线配置，判断机器是否需要执行

        Args:
            baseline_config ([type]): [description]

        Returns:
            [type]: [description]
        """

        if self.event_keyword not in baseline_config.event:
            return False

        # 如果没有正则，则全部匹配
        if not baseline_config.regex:
            return True
        
        match_hostname = re.match(baseline_config.regex.hostname, self.hostname)
        match_ip       = re.match(baseline_config.regex.ip, self.ip)
        match_arch     = re.match(baseline_config.regex.arch, self.arch)

        return match_arch and match_ip and match_hostname
    
    def check_all(self, baseline_configs):
        for event in baseline_configs:
            self.check_baseline_event(event)

    def check_baseline_event(self, baseline_event):        
        if not self.match(baseline_event):
            return

        config_results = []
        for config_item in baseline_event.configs:
            result = self.check_config_item(config_item)
            if result:
                config_results.append(result)
                
        event_result = baseline_event.__dict__
        event_result.pop('configs')
        event_result['configs_result'] = config_results
        self.results.append(event_result)

    def export_results(self):
        for event_result in self.results:
            # todo name tuple 换成dict
            # event_result['regex'] = dict(event_result.get('regex')._asdict())
            event_result.pop('regex')
            event_result['system_platform'] = self.system_platform
            event_result['hostname'] = self.hostname
            cur_configs = []
            for baseline_item_result in event_result.get('configs_result'):
                # 不受支持的基线检测类型
                # 添加 None
                if not baseline_item_result:
                    cur_configs.append(None)
                    continue
                cur_args_results = []
                for arg_result in baseline_item_result.args_result:
                    cur_args_results.append(arg_result.__dict__)
                baseline_item_result.args_result = cur_args_results
                cur_configs.append(baseline_item_result.__dict__)
            event_result['configs_result'] = cur_configs

        with open(BASELINE_RESULT, mode='w+') as fin:
            json.dump(self.results, fin, ensure_ascii=False, indent=2)
        os.chmod(BASELINE_RESULT, 0o644)

    def check_config_item(self, baseline_config_item):
        plugin = self.plugins.get(baseline_config_item.checker_type)
        if not plugin:
            print('{0} is not registered'.format(baseline_config_item.checker_type))
            return None
        else:
            return plugin.check(baseline_config_item)
