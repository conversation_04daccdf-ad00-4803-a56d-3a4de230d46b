[{"name": "密码审计基线", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "centos 7.6", "level": 1, "configs": [{"name": "配置文件检测", "category": "path_compare", "description": "目标路径需要同源路径一致", "args": [{"src": "./redhat_7/config/default/etc/acpi/events/powerconf", "dest": "/etc/acpi/events/powerconf"}]}, {"name": "配置文件配置包含", "category": "path_config_need", "description": "目标配置文件配置项需要包含原配置文件内的配置项目", "args": [{"src": "/compare/etc/asdasd/", "dest": "/etc/asdasd"}]}, {"name": "密码策略文件权限检查", "category": "path_permission", "description": "目标路径存在且权限一致", "args": [{"src": "/etc/passwd", "permission": "644", "owner": "root", "owner_group": "root"}, {"src": "/etc/shadow", "permission": "000", "owner": "root", "owner_group": "root"}]}]}, {"name": "服务器通用基线", "manager_name": "dfliao", "implementer": "chen<PERSON>i", "regex": {"arch": ".*", "ip": ".*", "hostname": ".*"}, "system_platform": "centos 7.6", "level": 1, "configs": [{"name": "HIDS进程巡检", "category": "process_exist", "description": "HIDS进程是否正在运行", "args": [{"process_trait": "titanagent"}]}, {"name": "Flume进程巡检", "category": "process_exist", "description": "Flume日志检测进程是否正在运行", "args": [{"process_trait": "flume"}]}, {"name": "node_exporter", "category": "process_exist", "description": "node-exporter巡检", "args": [{"process_trait": "node_exporter"}]}, {"name": "process_exporter", "category": "process_exist", "description": "process_exporter巡检", "args": [{"process_trait": "process-exporter"}]}, {"name": "配置文件检测", "category": "path_config_need", "description": "目标路径需要同源路径一致", "args": [{"src": "/compare/etc/asdasd/", "dest": "/etc/asdasd"}]}, {"name": "配置文件配置包含", "category": "path_config_need", "description": "目标配置文件配置项需要包含原配置文件内的配置项目", "args": [{"src": "/compare/etc/asdasd/", "dest": "/etc/asdasd"}]}, {"name": "定时任务配置文件", "category": "path_permission", "description": "目标路径存在且权限一致", "args": [{"src": "/etc/crontab", "permission": "400", "owner": "", "owner_group": ""}]}, {"name": "Flume安装动作检测", "category": "path_permission", "description": "系统管理员是否安装flume", "args": [{"src": "/home/<USER>/flume", "permission": "", "owner": "logadmin", "owner_group": "logadmin"}]}, {"name": "HIDS安装动作检测", "category": "path_permission", "description": "系统管理员是否安装flume", "args": [{"src": "/etc/titanagent/", "permission": "700", "owner": "root", "owner_group": "root"}]}, {"name": "ADDM安装动作检测", "category": "path_permission", "description": "系统管理员是否安装ADDM", "args": [{"src": "/home/<USER>/", "permission": "700", "owner": "ADDMAdmin", "owner_group": "ADDMAdmin"}]}]}]