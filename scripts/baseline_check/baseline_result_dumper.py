#!/usr/bin/python
# coding=utf-8

import xlwt, os, json, subprocess

def is_pass(json_path):
    with open(json_path, 'r+') as f:
        events = json.load(f)

        for event in events:
            for config in event['configs_result']:
                for arg in config['args_result']:
                    if not arg['ans']:
                        return False
                    
        return True

class BaselineResultDumper():
    def __init__(self, error_only = True, raw_result_path = '/tmp/raw_baseline_results'):
        self.error_only = error_only
        self.raw_result_path = raw_result_path
        self.workbook = xlwt.Workbook(encoding='utf-8')
        self.ip_list = []

    def aggregate(self):
        for root, dirs, files in os.walk(self.raw_result_path, topdown = True):
            for file_name in files:
                file_path = os.path.join(root, file_name)
                ip = file_path.split('/')[3] #ip

                if self.error_only and is_pass(file_path):
                    continue

                self.ip_list.append(ip)

                sheet = BaselineResultSheet(self.workbook, ip)

                #print('processing sheet ' + ip)

                with open(file_path, 'r+') as f:
                    events = json.load(f)

                    for event in events:
                        sheet.baseline_json_dump(event, self.error_only)

    def dump(self, name):
        if len(self.ip_list)==0:
            return
        subprocess.call('mkdir -p /tmp/baseline_results/{}'.format(name), shell=True)
        self.workbook.save('/tmp/baseline_results/{}/details.xls'.format(name)) 
        with open ('/tmp/baseline_results/{}/ip_list.xls'.format(name), 'w') as f:
            for ip in self.ip_list:
                f.write(ip + '\n')
            
        # if self.error_only:
        #     self.workbook.save('/tmp/errors.xls')
        # else:
        #     self.workbook.save('/tmp/fulldump.xls')

class BaselineResultSheet():
    def __init__(self, workbook, sheetname):
        #sheet settings
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 7000
        self.worksheet.col(1).width = 10000
        self.worksheet.col(2).width = 10000
        self.worksheet.col(3).width = 2000
        self.worksheet.col(4).width = 10000
        self.worksheet.col(5).width = 10000

        #style settings
        # self.style = xlwt.XFStyle()
        # alignment = xlwt.Alignment()
        # alignment.horz = 0x01
        #self.style.alignment = alignment

        self.row = 0
        self.result_write('巡检事务', '配置项', '描述', '结果', '原因', '参数')        

    def result_write(self, event, config, desc, result, reason, args):
        self.worksheet.write(self.row, 0, event)
        self.worksheet.write(self.row, 1, config)
        self.worksheet.write(self.row, 2, desc)
        self.worksheet.write(self.row, 3, result)
        self.worksheet.write(self.row, 4, reason)
        self.worksheet.write(self.row, 5, args)
        self.row += 1

    def baseline_json_dump(self, event, error_only = False):
        
        for config in event['configs_result']:
            for arg in config['args_result']:
                if error_only and arg['ans']:
                    continue

                self.result_write(event['name'], config['name'], config['description'], str(arg['ans']), arg['des'], str(json.dumps(arg['arg'], indent=4, ensure_ascii=False)))
