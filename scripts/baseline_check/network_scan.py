# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys, os, multiping, tcpping, openpyxl, pyexcel
reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
hids_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(hids_path)) #../
utils_path = root_path + '/utils' #../utils

sys.path.append(public_resources)
sys.path.append(root_path)
sys.path.append(utils_path)

from utils import my_print

if __name__ == '__main__':
    pyexcel.save_book_as(file_name='/tmp/baseline_results/未检查主机列表.xls', dest_file_name='/tmp/baseline_results/未检查主机列表.xlsx')
    book = openpyxl.load_workbook('/tmp/baseline_results/未检查主机列表.xlsx')
    #sheet = book.active
    sheet = book['脚本执行失败列表']
    ip_list = []
    for row in range(2, sheet.max_row + 1):
        ip = sheet.cell(column=1, row=row).value.strip()
        ip_list.append(ip)

    mp = multiping.MultiPing(ip_list)
    mp.send()

    l3_passed, l3_failed = mp.receive(1)
    l3_passed = l3_passed.keys()

    l4_passed = []
    l4_failed = []
    for ip in l3_passed:
        print('testing tcp connectivity of {}'.format(ip))
        test_passed = tcpping.tcpping(ip, '22', 3)
        if test_passed:
            l4_passed.append(ip)
        else:
            l4_failed.append(ip)

    print('total ip tested: {}'.format(len(ip_list)))
    print('L3 passed:       {}'.format(len(l3_passed)))
    print('L4 passed:       {}'.format(len(l4_passed)))

    sheet.cell(column=5, row=1).value = 'L3连通性'
    sheet.cell(column=6, row=1).value = 'L4连通性'
    for row in range(2, sheet.max_row + 1):
        ip = sheet.cell(column=1, row=row).value.strip()
        sheet.cell(column=5, row=row).value = ip in l3_passed
        sheet.cell(column=6, row=row).value = ip in l4_passed

    book.save('/tmp/network_scan.xlsx')