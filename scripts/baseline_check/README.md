基线巡检主要使用场景
1.进行基线巡检，巡检结果保存在/tmp目录下：
python ./baseline_control_node.py

2.进行基线巡检，只检查指定管理员管理的机器：
python ./baseline_control_node.py -u {manager_name}

3.进行基线巡检，只检查包含指定关键字的巡检事项：
python ./baseline_control_node.py -k {keyword}

4.指定配置文件进行基线巡检：
python ./baseline_control_node.py -c {config_file}

5.只在本机进行基线巡检，巡检结果保存在/tmp目录下：
python ./baseline_control_node.py -l

6.进行基线巡检，并将结果邮件发送给开发人员：
python ./baseline_control_node.py -m

7.进行基线修复，只检查包含指定关键字的巡检事项(因基线修复功能仍在开发阶段，未经过严格测试，安全起见，必须小心指定巡检事项，目前在ops机器上验证过的有：profile.d, audit, general-purpose)，修复结果存在/tmp目录下：
python baseline_control_node.py -f -k {keyword}

8.查看指定巡检项目配置及参数
python config_audit.py -a -k 密码审计基线

9.巡检完成后对未成功执行脚本的主机进行网络连通性测试：
root权限下执行python network_scan.py，结果保存在/tmp/network_scan.xlsx

10.网络连通性测试完成后检查ansible vault文件中是否包含未成功执行脚本的主机IP：
python vault_ips_check.py，结果保存在/tmp/network_scan.xlsx