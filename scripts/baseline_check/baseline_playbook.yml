---
- name: check baseline
  hosts: all
  gather_facts: no
  vars:
    - default_user: "roota"
  tasks:
    - name: Find and set connection
      set_fact:
        select_connection: >
          {{
            (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list | first)
            if (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list) | length > 0
            else vars[inventory_hostname].connections[0]
          }}
    - name: set fact from connection
      set_fact:
        ansible_user: "{{ select_connection.user }}"
        ansible_ssh_pass: "{{ select_connection.password }}"
    - name: remove baseline check files      
      shell: rm /tmp/baseline_check_* -rf
    - name: transfer baseline_files
      copy:
        src: "/tmp/baseline_check_files.tar.gz"
        dest: "/tmp"
        mode: '0755'
    - name: unarchive
      unarchive:
        src: /tmp/baseline_check_files.tar.gz
        dest: /tmp/
        remote_src: yes
    - name: setup runtime environment
      shell: cd /tmp/baseline_check_files/ && sudo bash ./env_setup.sh
    - name: run check scripts
      shell: cd /tmp/baseline_check_files/ && sudo python ./baseline_check.py
    - name: collect baseline results
      fetch:
        src: "/tmp/baseline_check_files/result.json"
        dest: "/tmp/raw_baseline_results"
