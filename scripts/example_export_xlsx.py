# -*- coding: utf-8 -*-
# !/usr/bin/python

import sys

if sys.version_info[0] < 3:
    reload(sys)
    sys.setdefaultencoding('utf-8')
# else:
#     import importlib
#     importlib.reload(sys)

sys.path.append('../')
import collections
from utils import CMDBApi
from utils.export_xlsx import XlsExporter
from utils.my_mail import *

from resources.config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host

cmdb_api = CMDBApi(
    access_key=cmdb_api_access_key,
    secret_key=cmdb_api_secret_key,
    host=cmdb_api_host
)

if __name__ == '__main__':
    # print("请输入IP，ctrl+d结束输入（未做严格输入验证，请确保仅有IP字符串）")
    # checked_ip = [a.strip() for a in sys.stdin.readlines() if a]
    # # checked_ip = [a.strip() for a in input().split('\n') if a]
    # print("################################################")
    device_ips = [u'*************', u'*************', u'*************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'*************', u'*************', u'*************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'*************', u'*************', u'*************', u'*************', u'*************', u'*************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************', u'************']

    # ip_seg = ['10.132', '10.133', '10.134', '10.182', '10.183', '10.192', '10.193', '10.212', '10.213', '10.142']
    # for ips in checked_ip:
    #     have_check = False
    #     for ip in sorted(ips.split(',')):
    #         for _ in ip_seg:
    #             if _ in ip:
    #                 device_ips.append(ip)
    #                 have_check = True
    #                 break
    #         if have_check:
    #             break
    #
    # device_ips = sorted(list(set(device_ips)))
    all_device_data = cmdb_api.get_host_data(
        query={
            "ip": {"$in": device_ips},
            "userGroupManager": "服务器及存储基础设施组",
            "$or": [
                # {"osDistroinit": {"$like": '%red%'}},
                # {"osDistro": {"$like": '%centos%'}},
                {"osDistro": {"$like": '%Kylin%'}}
            ],
        },
        fields={
            "ip": True,
            "hostname": True,
            "status": True,
            "sn": True,
            "model": True,
            "item": True,
            "deviceVersion": True,
            "layer": True,
            "userGroupManager": True,
            "managerA": True,
            "managerB": True,
            "manageIP": True,
            "outBandIP": True,
            "defaultIP": True,
            "businessIP": True,
            "osDistro": True,
            "assetsSn": True,
            "mdl": True,
            "physicalServer.outBandIP": True,
            "base_physical_server": True,
            "_openstackserver._host._host__IDCRACK._rack__IDC": True,
            "vmware_vm.VMWARE_HOST_COMPUTER._idc": True,
            "physicalServer.rack.name": True,
            "APPSZSE.nameN": True,
            "APPSZSE.name": True,
            "APPSZSE.equalPLevel": True,
            "APPSZSE.securityLevel": True,
            "APPSZSE.impLevel": True,
        }
    )
    not_fount_ips = set()
    # if len(all_device_data) != len(device_ips):
    #     not_fount_ips = set(device_ips) - set([a.get('ip') for a in all_device_data])
    #     print('以下数据未查询到')
    #     for ip in not_fount_ips:
    #         print(ip)

    xlsx_path = '/tmp/tmp.xlsx'
    xe = XlsExporter(xlsx_path)

    DEVICES_VARS = collections.OrderedDict()
    DEVICES_VARS['主机名'] = lambda x: x.get('hostname', 'CMDB未查询到')
    DEVICES_VARS['管理员A'] = lambda x: x.get('managerA', 'CMDB未查询到')
    DEVICES_VARS['管理员B'] = lambda x: x.get('managerB', 'CMDB未查询到')
    DEVICES_VARS['主机OS类型'] = lambda x: x.get('osDistro', '其他')
    DEVICES_VARS['设备在线'] = lambda x: x.get('status', 'CMDB未查询到')
    DEVICES_VARS['主机IP'] = lambda x: x.get('ip', 'CMDB未查询到')
    DEVICES_VARS['型号'] = lambda x: x.get('model', 'CMDB未查询到')
    DEVICES_VARS['序列号'] = lambda x: x.get('sn', 'CMDB未查询到')
    DEVICES_VARS['部门组别'] = lambda x: x.get('userGroupManager', 'CMDB未查询到')
    DEVICES_VARS['所属应用'] = lambda x: ",".join(sorted([a.get('nameN','CMDB未查询到') for  a in x.get('APPSZSE', [])]))

    xe.write_devices(sheet_name='导出机器', devices=all_device_data, row_vars=DEVICES_VARS, )
    # xe.write_devices(sheet_name='未找到IP的机器', devices={'ip': ip for ip in not_fount_ips}, row_vars=DEVICES_VARS, )
    xe.save_workbook()

    message = []
    # message.append(mail_html(devices_to_html_table('漏洞修复', devices, row_vars=DEVICES_VARS).to_html()))
    message.append(mail_file(xlsx_path, 'xlsx', '导出机器.xlsx'))
    from resources.config import mail_user, mail_host, mail_password

    send_mail(['<EMAIL>', ''], '导出机器', contents=message, mail_user=mail_user, mail_host=mail_host,
              mail_password=mail_password)
