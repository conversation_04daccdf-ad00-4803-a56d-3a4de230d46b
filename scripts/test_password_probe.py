# -*- coding: utf-8 -*-
# !/usr/bin/python

"""
测试密码探测功能
"""

import sys
import os
import tempfile
import json
sys.path.append('../')

from my_ansible import MyAnsibleVaultManager


def test_vault_manager_basic():
    """
    测试vault管理器基本功能
    """
    print("测试1: Vault管理器基本功能")
    print("-" * 30)
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.vault') as f:
        temp_vault_file = f.name
    
    try:
        # 测试数据
        test_data = {
            "localhost": {
                "connections": [
                    {"user": "testuser", "password": "testpass"}
                ]
            },
            "*************": {
                "connections": [
                    {"user": "root", "password": "invalid_password"},
                    {"user": "admin", "password": "another_invalid"}
                ]
            }
        }
        
        vault_password = "test_password"
        
        # 创建vault管理器
        vault_manager = MyAnsibleVaultManager(temp_vault_file, vault_password)
        vault_manager.vault_vars = test_data
        
        # 测试加密
        vault_manager.encrypt_file()
        print("✓ 加密测试通过")
        
        # 测试解密
        vault_manager.vault_vars = None
        vault_manager.decrypt_file()
        print("✓ 解密测试通过")
        
        # 验证数据
        assert vault_manager.vault_vars == test_data
        print("✓ 数据完整性测试通过")
        
        # 测试统计功能
        stats = vault_manager.get_vault_statistics()
        assert stats['total_hosts'] == 2
        assert stats['total_connections'] == 3
        assert 'testuser' in stats['users']
        assert 'root' in stats['users']
        print("✓ 统计功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_vault_file):
            os.unlink(temp_vault_file)


def test_connection_removal():
    """
    测试连接删除功能
    """
    print("\n测试2: 连接删除功能")
    print("-" * 30)
    
    try:
        # 创建测试vault管理器
        vault_manager = MyAnsibleVaultManager("dummy", "dummy")
        vault_manager.vault_vars = {
            "host1": {
                "connections": [
                    {"user": "user1", "password": "pass1"},
                    {"user": "user2", "password": "pass2"}
                ]
            },
            "host2": {
                "connections": [
                    {"user": "user3", "password": "pass3"}
                ]
            }
        }
        
        # 测试删除连接
        vault_manager._remove_invalid_connection("host1", {"user": "user1", "password": "pass1"})
        
        # 验证删除结果
        host1_connections = vault_manager.vault_vars["host1"]["connections"]
        assert len(host1_connections) == 1
        assert host1_connections[0]["user"] == "user2"
        print("✓ 连接删除测试通过")
        
        # 测试清理空主机
        vault_manager.vault_vars["host3"] = {"connections": []}
        vault_manager._cleanup_empty_hosts()
        
        assert "host3" not in vault_manager.vault_vars
        print("✓ 空主机清理测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_probe_summary():
    """
    测试探测结果摘要功能
    """
    print("\n测试3: 探测结果摘要功能")
    print("-" * 30)
    
    try:
        vault_manager = MyAnsibleVaultManager("dummy", "dummy")
        
        # 模拟探测结果
        test_results = {
            'tested': 5,
            'reachable': 3,
            'unreachable': 2,
            'removed_connections': [
                {'host_ip': '*************', 'user': 'root'},
                {'host_ip': '*************', 'user': 'admin'}
            ],
            'valid_connections': [
                {'host_ip': '*************', 'user': 'root'},
                {'host_ip': '*************', 'user': 'user1'},
                {'host_ip': '*************', 'user': 'user2'}
            ],
            'errors': [
                {'host_ip': '*************', 'user': 'test', 'error': 'Connection timeout'}
            ]
        }
        
        # 测试摘要打印（不会抛出异常即为成功）
        vault_manager._print_probe_summary(test_results)
        print("✓ 探测结果摘要测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_vault_statistics():
    """
    测试vault统计功能
    """
    print("\n测试4: Vault统计功能")
    print("-" * 30)
    
    try:
        vault_manager = MyAnsibleVaultManager("dummy", "dummy")
        vault_manager.vault_vars = {
            "host1": {
                "connections": [
                    {"user": "root", "password": "pass1"},
                    {"user": "admin", "password": "pass2"}
                ]
            },
            "host2": {
                "connections": [
                    {"user": "root", "password": "pass3"},
                    {"user": "user1", "password": "pass4"}
                ]
            },
            "host3": {
                "connections": []
            }
        }
        
        stats = vault_manager.get_vault_statistics()
        
        # 验证统计结果
        assert stats['total_hosts'] == 3
        assert stats['hosts_with_connections'] == 2
        assert stats['total_connections'] == 4
        assert len(stats['users']) == 3  # root, admin, user1
        assert 'root' in stats['users']
        assert 'admin' in stats['users']
        assert 'user1' in stats['users']
        
        # 验证用户分布
        assert len(stats['hosts_by_user']['root']) == 2  # host1, host2
        assert len(stats['hosts_by_user']['admin']) == 1  # host1
        assert len(stats['hosts_by_user']['user1']) == 1  # host2
        
        print("✓ Vault统计功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_error_handling():
    """
    测试错误处理
    """
    print("\n测试5: 错误处理")
    print("-" * 30)
    
    try:
        # 测试不存在的文件
        vault_manager = MyAnsibleVaultManager("nonexistent_file", "password")
        vault_manager.decrypt_file()  # 应该不会抛出异常，只是打印错误
        print("✓ 文件不存在错误处理测试通过")
        
        # 测试空vault数据的统计
        vault_manager.vault_vars = None
        stats = vault_manager.get_vault_statistics()
        assert stats == {}
        print("✓ 空数据错误处理测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def run_all_tests():
    """
    运行所有测试
    """
    print("开始运行密码探测功能测试")
    print("=" * 50)
    
    tests = [
        test_vault_manager_basic,
        test_connection_removal,
        test_probe_summary,
        test_vault_statistics,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False


if __name__ == '__main__':
    success = run_all_tests()
    
    if not success:
        sys.exit(1)
    
    print("\n注意事项:")
    print("1. 这些是基本功能测试，不包含实际SSH连接测试")
    print("2. 实际SSH连接测试需要有效的主机和凭据")
    print("3. 运行 demo_password_probe.py 查看完整功能演示")
    print("4. 使用 vault_password_probe.py 进行实际的密码探测")
