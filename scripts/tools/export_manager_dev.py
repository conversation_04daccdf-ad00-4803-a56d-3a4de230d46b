# -*- coding: utf-8 -*-
# !/usr/bin/python

import os
import sys
import openpyxl

reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
baseline_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(baseline_path))
utils_path = root_path + '/utils'           # ./utils
#resource_path = root_path + '/resources'    # ./resources

sys.path.append(root_path)
sys.path.append(utils_path)
sys.path.append(public_resources)

from utils.my_mail import *
from utils import CMDBApi
from utils import my_print
from item import *

from config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host

cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

if __name__ == '__main__':
    query = {
        "userGroupManager": "服务器及存储基础设施组",
    }

    fields = {            
        "hostname": True,
        "ip": True,
        "status": True,
        "sn": True,
        "model": True,
        "item": True,
        "layer": True,
        "userGroupManager": True,
        "managerA": True,
        "managerB": True,
        "osDistro": True
    }

    hosts = cmdb_api.get_host_data(query, fields)

    book = openpyxl.Workbook()
    sheet = book.active

    column = 1
    for key in fields.keys():
        sheet.cell(column=column, row=1).value = key
        column += 1

    row = 2
    for host in hosts:
        column = 1
        for key in fields.keys():
            sheet.cell(column=column, row=row).value = host.get(key)
            column += 1
        row += 1
    
    book.save('/tmp/hosts.xlsx')