# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys, os
reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
hids_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(hids_path)) #../
utils_path = root_path + '/utils' #../utils

sys.path.append(public_resources)
sys.path.append(root_path)
sys.path.append(utils_path)

import hashlib
import json
import time
import argparse
import requests
import sqlalchemy
import xlwt
import subprocess
import yaml
import getpass

from db_connection import SqliteMemoryConnection, MysqlConnection
from item import Device_MEM, HIDSBlackList, HIDSExemptionListNew
from utils import devices_to_html_table
from utils import CMDBApi
from utils import my_print

from config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host
cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

blacklist_db_connection = MysqlConnection()

# get devices info from cmdb
def get_cmdb_devices(running_only = True, linux_only = False, hostname = None, appname = None):

    query = dict()
    query['userGroupManager'] = "服务器及存储基础设施组"
    if running_only:
        query['status'] = {"$eq": "运行中"}

    query['$or'] = []
    query['$and'] = []

    if linux_only:
        query['$or'].append({"osDistro": {"$like": '%red%'}})
        query['$or'].append({"osDistro": {"$like": '%centos%'}})
        query['$or'].append({"osDistro": {"$like": '%Kylin%'}})

    if hostname:
        #query['hostname'] = {"$eq": hostname}
        query['$and'].append({"hostname": {"$like": '%{}%'.format(hostname)}})
        
    if appname:
        #query['hostname'] = {"$eq": hostname}
        # query['$or'] = [
        #    {"hostname": {"$like": '%{}%'.format(hostname)}}
        # ]
        query['$and'].append({"APPSZSE.name": {"$like": '%{}%'.format(appname)}})
    
    fields = {
        "instanceID": True,
        "hostname": True,
        "ip": True,
        "item": True,
        "model": True,
        "status": True,
        "osDistro": True,
        "managerA": True,
        "managerB": True,
        "userGroupManager": True,
        "physicalServer.rack.name": True
    }

    #devices = cmdb_api.get_all_data(query, fields)
    devices = cmdb_api.get_host_data(query)

    return devices

def read_vault_file(vault_file):
    cmd = ['ansible-vault', 'view', vault_file, '--ask-vault-pass']
    process = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        raise Exception(stderr.decode())

    return yaml.safe_load(stdout)

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='cmdb fetch')
    parser.add_argument('-a', '--appname', type=str, default='', help='app_name trait')
    parser.add_argument('-n', '--hostname', type=str, default='', help='host_name trait')
    args = parser.parse_args()

    devices = get_cmdb_devices(running_only=False, linux_only=False, hostname=args.hostname, appname=args.appname)

    conn_dict = read_vault_file('/home/<USER>/resources/baseline_vault')

    for dev in devices:
        conn = conn_dict.get(dev['ip'])

        if conn:
            info = {}
            info['hostname'] = dev.get('hostname')
            info['ip'] = dev.get('ip')
            info['os'] = dev.get('osDistro')
            info['type'] = dev.get('item')
            info['description'] = dev.get('model')
            info['connections'] = conn
            my_print(info)