#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Vault密码探测工具

用于检测ansible vault文件中保存的SSH密码是否仍然有效，
自动删除无效的连接信息。

使用方法:
    python vault_password_probe.py -f vault_file -p vault_password
    python vault_password_probe.py -f vault_file --interactive
    python vault_password_probe.py -f vault_file -p vault_password --dry-run
"""

import sys
import argparse
import getpass
import os
import json
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from my_ansible import MyAnsibleVaultManager, probe_vault_passwords


def main():
    parser = argparse.ArgumentParser(
        description='Vault密码探测工具 - 检测并清理无效的SSH连接信息',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s -f /path/to/vault_file -p mypassword
  %(prog)s -f /path/to/vault_file --interactive
  %(prog)s -f /path/to/vault_file -p mypassword --dry-run --timeout 10
  %(prog)s -f /path/to/vault_file -p mypassword --analyze-only
        """
    )
    
    parser.add_argument('-f', '--file', required=True,
                        help='Vault文件路径')
    parser.add_argument('-p', '--password',
                        help='Vault密码 (如果不提供将提示输入)')
    parser.add_argument('--interactive', action='store_true',
                        help='交互模式，提示输入密码')
    parser.add_argument('--dry-run', action='store_true',
                        help='试运行模式，不保存更改')
    parser.add_argument('--analyze-only', action='store_true',
                        help='仅分析vault文件，不进行连接测试')
    parser.add_argument('--timeout', type=int, default=5,
                        help='SSH连接超时时间 (默认: 5秒)')
    parser.add_argument('--max-workers', type=int, default=10,
                        help='最大并发连接数 (默认: 10)')
    parser.add_argument('--output', '-o',
                        help='将结果保存到JSON文件')
    parser.add_argument('--backup', action='store_true',
                        help='在修改前创建vault文件备份')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='详细输出')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"错误: 文件不存在: {args.file}")
        sys.exit(1)
    
    # 获取vault密码
    if args.interactive or not args.password:
        vault_password = getpass.getpass("请输入vault密码: ")
    else:
        vault_password = args.password
    
    if not vault_password:
        print("错误: 必须提供vault密码")
        sys.exit(1)
    
    try:
        # 创建vault管理器
        vault_manager = MyAnsibleVaultManager(args.file, vault_password)
        
        # 解密vault文件
        print(f"正在解密vault文件: {args.file}")
        vault_manager.decrypt_file()
        
        if not vault_manager.vault_vars:
            print("错误: Vault文件为空或解密失败")
            sys.exit(1)
        
        # 分析vault文件
        print("\n" + "="*60)
        print("Vault文件分析")
        print("="*60)
        
        stats = vault_manager.get_vault_statistics()
        print(f"总主机数: {stats['total_hosts']}")
        print(f"有连接的主机数: {stats['hosts_with_connections']}")
        print(f"总连接数: {stats['total_connections']}")
        print(f"用户列表: {', '.join(stats['users'])}")
        
        if args.verbose:
            print(f"\n用户分布:")
            for user, hosts in stats['hosts_by_user'].items():
                print(f"  {user}: {len(hosts)} 台主机")
                if len(hosts) <= 5:
                    for host in hosts:
                        print(f"    - {host}")
                else:
                    for host in hosts[:3]:
                        print(f"    - {host}")
                    print(f"    ... 还有 {len(hosts)-3} 台主机")
        
        # 如果只是分析模式，直接退出
        if args.analyze_only:
            print("\n分析完成 (仅分析模式)")
            return
        
        # 创建备份
        if args.backup:
            backup_file = f"{args.file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(args.file, backup_file)
            print(f"✓ 已创建备份文件: {backup_file}")
        
        # 执行密码探测
        print("\n" + "="*60)
        print("开始密码探测")
        print("="*60)
        
        results = vault_manager.probe_vault_passwords(
            timeout=args.timeout,
            max_workers=args.max_workers,
            save_changes=not args.dry_run
        )
        
        # 保存结果到文件
        if args.output:
            output_data = {
                'timestamp': datetime.now().isoformat(),
                'vault_file': args.file,
                'probe_results': results,
                'vault_stats_before': stats,
                'vault_stats_after': vault_manager.get_vault_statistics(),
                'settings': {
                    'timeout': args.timeout,
                    'max_workers': args.max_workers,
                    'dry_run': args.dry_run
                }
            }
            
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            print(f"✓ 结果已保存到: {args.output}")
        
        # 显示最终状态
        if args.dry_run:
            print("\n注意: 这是试运行模式，未保存任何更改")
        elif results['removed_connections']:
            print(f"\n✓ 已从vault文件中删除 {len(results['removed_connections'])} 个无效连接")
        
        # 显示建议
        if results['unreachable'] > 0:
            print(f"\n建议:")
            print(f"- 检查网络连接和防火墙设置")
            print(f"- 确认SSH服务是否正常运行")
            print(f"- 验证用户名和密码是否正确")
        
        if results['errors']:
            print(f"- 检查出错的连接，可能需要手动处理")
        
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def check_dependencies():
    """检查依赖项"""
    try:
        import paramiko
    except ImportError:
        print("错误: 缺少paramiko库，请安装:")
        print("pip install paramiko")
        sys.exit(1)


if __name__ == '__main__':
    check_dependencies()
    main()
