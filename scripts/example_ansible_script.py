# -*- coding: utf-8 -*-
# !/usr/bin/python
import copy
import sys

if sys.version_info[0] < 3:
    reload(sys)
    sys.setdefaultencoding('utf-8')
else:
    import importlib

    importlib.reload(sys)
sys.path.append('../')

from my_ansible import *
import getpass


def _init_mas(ips=None, var_file='/home/<USER>/resources/baseline_vault'):
    """
    ips： ip列表，未做严格限制
    var_file: 变量文件路径
    """
    if not ips:
        print("请输入IP，ctrl+d结束输入（未做严格输入验证，请确保仅有IP字符串）")
        ips = [a.strip() for a in sys.stdin.readlines() if a]

    if len(ips) == 1:
        inv = ips[0] + ','
    else:
        inv = ','.join(ips)

    mas = MyAnsibleService(
        vault_pass=getpass.getpass('Plesa input Vaultpass: '),
        password_vault_path=var_file,
        timeout=1,
        inventory=inv,
        forks=16, syntax=None, verbosity=3,
        become=True, become_method='sudo', become_user='root',
    )
    mas.load_vault_file()
    return mas


def run_playbook(playbook_paths, ips=None, var_file='/home/<USER>/resources/baseline_vault'):
    """
    playbook_paths: [playbook_path,] playbook路径列表
    ips： ip列表，未做严格限制
    var_file: 变量文件路径
    """
    mas = _init_mas(ips=ips, var_file=var_file)
    mas.playbook(playbooks=playbook_paths)
    failed_ips = sorted(list(set(mas.results_callback.host.get('failed', {}).keys()) | set(
        mas.results_callback.host.get('unreachable', {}).keys())))

    for task in mas.results_callback.all_info:
        if task['result_info'].get('msg'):
            print(task['host_ip'])
            print(task['result_info'].get('msg'))

        if ['task_name'] == 'show answer':
            ip_ans = json.loads(task['result_info']['stdout_lines'][0])
    return mas


def run_play(plays, ips=None, default_user='roota', var_file='/home/<USER>/resources/baseline_vault', become=None):
    mas = _init_mas(ips=ips, var_file=var_file)
    from my_ansible import DEFAULT_VAULT_PLAY_SOURCE
    cur_play = copy.deepcopy(DEFAULT_VAULT_PLAY_SOURCE)
    extra_vars = {'default_user': default_user}
    for play in plays:
        cur_play['tasks'].append(play)
    mas.run(cur_play, extra_vars=extra_vars)
    return mas


if __name__ == '__main__':
    mas = run_play(
        ips=['************', '************'],
        plays=[
            {
                "name": "Display machine name",
                "action": "debug",
                "args": {
                    "msg": "Machine name: {{ ansible_facts.hostname }}"
                }
            },
            {
                "name": "Remove Ce* files from yum.repos.d",
                "action": "file",
                "args": {
                    "path": "/etc/yum.repos.d/Ce*",
                    "state": "absent"
                }
            },
            {
                "name": "List /etc/yum.repos.d/",
                "action":"Shell",
                "args":{
                    "cmd" : "whoami && ls /etc/yum.repos.d/"
                }
            }
            # {
            #     "name": "Update and upgrade packages",
            #     "action": "yum",
            #     "args": {
            #         "name": "{{ item }}",
            #         "update_cache": True
            #     },
            #     "loop": {
            #         "with_items": ["update", "upgrade"]
            #     }
            # },
            # {
            #     "name": "Remove Ce* files from yum.repos.d again",
            #     "action": "file",
            #     "args": {
            #         "path": "/etc/yum.repos.d/Ce*",
            #         "state": "absent"
            #     }
            # },
            # {
            #     "name": "Reboot the machine",
            #     "action": "reboot",
            #     "args": {
            #         "msg": "Rebooting to apply updates",
            #         "connect_timeout": 5,
            #         "reboot_timeout": 300,
            #         "pre_reboot_delay": 0,
            #         "test_command": "uptime"
            #     }
            # }
        ],
        become=True
    )
