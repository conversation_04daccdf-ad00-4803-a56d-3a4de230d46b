# -*- coding: utf-8 -*-
# !/usr/bin/python

import sys
if sys.version_info[0] < 3:
    reload(sys)
    sys.setdefaultencoding('utf-8')
else:
    import importlib
    importlib.reload(sys)

sys.path.append('../')
import copy
from my_ansible import *
import getpass


def _init_mas(ips=None, var_file='/home/<USER>/resources/baseline_vault'):
    """
    ips： ip列表，未做严格限制
    var_file: 变量文件路径
    """
    if not ips:
        print("请输入IP，ctrl+d结束输入（未做严格输入验证，请确保仅有IP字符串）")
        ips = [a.strip() for a in sys.stdin.readlines() if a]

    if len(ips) == 1:
        inv = ips[0] + ','
    else:
        inv = ','.join(ips)

    mas = MyAnsibleService(
        vault_pass=getpass.getpass('Plesa input Vaultpass: '),
        password_vault_path=var_file,
        timeout=1,
        inventory=inv,
        forks=16, syntax=None, verbosity=3,
        become=True, become_method='sudo', become_user='root',
    )
    mas.load_vault_file()
    return mas


def run_playbook(playbook_paths, ips=None, var_file='/home/<USER>/resources/baseline_vault'):
    """
    运行 Ansible Playbook

    Args:
        playbook_paths: playbook路径列表
        ips: IP列表
        var_file: 变量文件路径

    Returns:
        MyAnsibleService 实例
    """
    mas = _init_mas(ips=ips, var_file=var_file)
    mas.playbook(playbooks=playbook_paths)

    # 获取失败的IP
    failed_ips = mas.results_callback.get_all_failed_ip()

    # 处理任务结果
    _process_task_results(mas.results_callback.all_info)

    return mas


def _process_task_results(all_info):
    """
    处理任务结果，提取有用信息

    Args:
        all_info: 所有任务信息列表
    """
    for task in all_info:
        result_info = task.get('result_info', {})

        # 打印消息信息
        if result_info.get('msg'):
            print(f"Host: {task['host_ip']}")
            print(f"Message: {result_info.get('msg')}")
            print("-" * 50)

        # 修复原代码中的逻辑错误
        if task.get('task_name') == 'show answer':
            stdout_lines = result_info.get('stdout_lines', [])
            if stdout_lines:
                try:
                    import json
                    ip_ans = json.loads(stdout_lines[0])
                    print(f"Answer: {ip_ans}")
                except (json.JSONDecodeError, IndexError) as e:
                    print(f"Error parsing answer: {e}")


def run_play(plays, ips=None, default_user='roota', var_file='/home/<USER>/resources/baseline_vault', become=None):
    """
    运行 Ansible Play

    Args:
        plays: 任务列表
        ips: IP列表
        default_user: 默认用户
        var_file: 变量文件路径
        become: 是否提权

    Returns:
        MyAnsibleService 实例
    """
    mas = _init_mas(ips=ips, var_file=var_file)

    # 构建 play 结构
    play_source = _build_play_source(plays, default_user)
    extra_vars = {'default_user': default_user}

    # 执行 play
    result = mas.run(play_source, extra_vars=extra_vars)

    # 处理结果
    _handle_execution_results(mas)

    return mas


def _build_play_source(plays, default_user='roota'):
    """
    构建 play 源结构

    Args:
        plays: 任务列表
        default_user: 默认用户

    Returns:
        play 源字典
    """
    from my_ansible import DEFAULT_VAULT_PLAY_SOURCE
    play_source = copy.deepcopy(DEFAULT_VAULT_PLAY_SOURCE)

    # 添加任务到 play
    for play in plays:
        play_source['tasks'].append(play)

    return play_source


def _handle_execution_results(mas):
    """
    处理执行结果，打印状态信息

    Args:
        mas: MyAnsibleService 实例
    """
    # 获取失败和不可达的IP
    failed_ips = mas.results_callback.get_failed_ip()
    unreachable_ips = mas.results_callback.get_unreachable_ip()

    if failed_ips:
        print(f"Failed IPs: {failed_ips}")

    if unreachable_ips:
        print(f"Unreachable IPs: {unreachable_ips}")

    # 显示每个IP的详细执行结果
    _display_host_results(mas.results_callback)


def _display_host_results(callback):
    """
    显示每个主机的详细执行结果

    Args:
        callback: MyCallbackModule 实例
    """
    print("\n" + "="*60)
    print("每个主机的详细执行结果:")
    print("="*60)

    # 获取所有主机的摘要
    all_summaries = callback.get_all_hosts_summary()

    for host_ip, summary in all_summaries.items():
        print(f"\n主机: {host_ip}")
        print(f"  总任务数: {summary['total_tasks']}")
        print(f"  成功: {summary['ok']}")
        print(f"  失败: {summary['failed']}")
        print(f"  跳过: {summary['skipped']}")
        print(f"  不可达: {summary['unreachable']}")

        # 显示任务详情
        print(f"  任务详情:")
        for task in summary['tasks']:
            status_icon = {
                'ok': '✓',
                'failed': '✗',
                'skipped': '⊝',
                'unreachable': '⚠'
            }.get(task['status'], '?')

            print(f"    {status_icon} [{task['exec_time']}] {task['task_name']} - {task['status']}")

            # 如果有错误信息，显示错误详情
            if task['status'] in ['failed', 'unreachable']:
                result_info = task.get('result_info', {})
                if 'msg' in result_info:
                    print(f"      错误信息: {result_info['msg']}")
                elif 'stderr' in result_info:
                    print(f"      错误输出: {result_info['stderr']}")


def get_host_task_history(callback, host_ip):
    """
    获取指定主机的任务执行历史

    Args:
        callback: MyCallbackModule 实例
        host_ip: 主机IP

    Returns:
        list: 任务执行历史
    """
    return callback.get_host_results(host_ip)


def get_failed_tasks_for_host(callback, host_ip):
    """
    获取指定主机的失败任务

    Args:
        callback: MyCallbackModule 实例
        host_ip: 主机IP

    Returns:
        list: 失败的任务列表
    """
    return callback.get_tasks_by_status(host_ip, 'failed')


def create_basic_tasks():
    """
    创建基础任务列表

    Returns:
        任务列表
    """
    return [
        {
            "name": "Gather facts",
            "action": "setup"
        },
        {
            "name": "Display machine name",
            "action": "debug",
            "args": {
                "msg": "Machine name: {{ ansible_facts.get('nodename', 'Unknown') }}"
            }
        },
        {
            "name": "Check current user",
            "action": "shell",
            "args": {
                "cmd": "whoami"
            },
            "register": "current_user_result",
            "changed_when": False
        },
        {
            "name": "Show current user",
            "action": "debug",
            "args": {
                "msg": "Current user: {{ current_user_result.stdout }}"
            }
        },
        {
            "name": "List /etc/yum.repos.d/",
            "action": "shell",
            "args": {
                "cmd": "ls -la /etc/yum.repos.d/ || echo 'Directory not found'"
            },
            "register": "repos_list_result",
            "changed_when": False
        },
        {
            "name": "Show repos directory content",
            "action": "debug",
            "args": {
                "msg": "{{ repos_list_result.stdout_lines | default(['No output']) | join('\\n') }}"
            }
        }
    ]


def create_maintenance_tasks():
    """
    创建维护任务列表（注释掉的任务）

    Returns:
        维护任务列表
    """
    return [
        {
            "name": "Remove Ce* files from yum.repos.d",
            "action": "file",
            "args": {
                "path": "/etc/yum.repos.d/Ce*",
                "state": "absent"
            }
        },
        {
            "name": "Update and upgrade packages",
            "action": "yum",
            "args": {
                "name": "*",
                "state": "latest",
                "update_cache": True
            }
        },
        {
            "name": "Reboot the machine",
            "action": "reboot",
            "args": {
                "msg": "Rebooting to apply updates",
                "connect_timeout": 5,
                "reboot_timeout": 300,
                "pre_reboot_delay": 0,
                "test_command": "uptime"
            }
        }
    ]


if __name__ == '__main__':
    # 配置参数
    target_ips = ['************', '************']

    print("开始执行 Ansible 任务...")
    print(f"目标主机: {target_ips}")

    try:
        # 执行基础任务
        mas = run_play(
            ips=target_ips,
            plays=create_basic_tasks(),
            become=True
        )

        print("任务执行完成!")

        # 显示执行结果摘要
        mas.get_result()

    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
