# -*- coding: utf-8 -*-
# !/usr/bin/python

"""
演示新的主机结果管理功能
"""

import sys
import json
sys.path.append('../')

from example_ansible_script import run_play, create_basic_tasks


def demo_host_results():
    """
    演示每个IP的任务执行历史功能
    """
    print("演示：每个IP的任务执行历史功能")
    print("="*50)
    
    # 配置测试主机（使用localhost进行演示）
    target_ips = ['localhost']
    
    try:
        # 执行任务
        mas = run_play(
            ips=target_ips,
            plays=create_basic_tasks(),
            become=False  # 本地测试不需要提权
        )
        
        callback = mas.results_callback
        
        print("\n" + "="*50)
        print("功能演示：")
        print("="*50)
        
        # 1. 获取所有主机的结果
        print("\n1. 所有主机的结果概览:")
        all_host_results = callback.get_host_results()
        for host_ip, tasks in all_host_results.items():
            print(f"  主机 {host_ip}: {len(tasks)} 个任务")
        
        # 2. 获取特定主机的详细结果
        for host_ip in target_ips:
            print(f"\n2. 主机 {host_ip} 的详细结果:")
            host_tasks = callback.get_host_results(host_ip)
            
            for i, task in enumerate(host_tasks, 1):
                print(f"  任务 {i}: {task['task_name']}")
                print(f"    状态: {task['status']}")
                print(f"    执行时间: {task['exec_time']}")
                print(f"    Play名称: {task['play_name']}")
                
                # 显示结果信息（简化版）
                result_info = task.get('result_info', {})
                if 'msg' in result_info:
                    print(f"    消息: {result_info['msg']}")
                elif 'stdout' in result_info:
                    print(f"    输出: {result_info['stdout'][:100]}...")
                print()
        
        # 3. 获取主机摘要
        print(f"\n3. 主机摘要信息:")
        for host_ip in target_ips:
            summary = callback.get_host_summary(host_ip)
            print(f"  主机 {host_ip}:")
            print(f"    总任务数: {summary['total_tasks']}")
            print(f"    成功: {summary['ok']}")
            print(f"    失败: {summary['failed']}")
            print(f"    跳过: {summary['skipped']}")
            print(f"    不可达: {summary['unreachable']}")
        
        # 4. 获取特定状态的任务
        print(f"\n4. 成功的任务:")
        for host_ip in target_ips:
            ok_tasks = callback.get_tasks_by_status(host_ip, 'ok')
            print(f"  主机 {host_ip} 成功的任务:")
            for task in ok_tasks:
                print(f"    - {task['task_name']}")
        
        # 5. 导出结果到JSON文件
        print(f"\n5. 导出结果到文件:")
        export_results_to_json(callback, 'host_results.json')
        
        return True
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def export_results_to_json(callback, filename):
    """
    将主机结果导出到JSON文件
    
    Args:
        callback: MyCallbackModule 实例
        filename: 输出文件名
    """
    try:
        # 获取所有主机的完整结果
        all_results = {
            'summary': callback.get_all_hosts_summary(),
            'detailed_results': callback.get_host_results(),
            'export_time': callback.all_info[0]['exec_time'] if callback.all_info else None
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"    结果已导出到: {filename}")
        
    except Exception as e:
        print(f"    导出失败: {e}")


def analyze_host_performance(callback, host_ip):
    """
    分析主机性能（示例功能）
    
    Args:
        callback: MyCallbackModule 实例
        host_ip: 主机IP
    """
    host_tasks = callback.get_host_results(host_ip)
    
    if not host_tasks:
        print(f"主机 {host_ip} 没有任务记录")
        return
    
    print(f"\n主机 {host_ip} 性能分析:")
    
    # 计算成功率
    total_tasks = len(host_tasks)
    successful_tasks = len([t for t in host_tasks if t['status'] == 'ok'])
    success_rate = (successful_tasks / total_tasks) * 100 if total_tasks > 0 else 0
    
    print(f"  任务成功率: {success_rate:.1f}% ({successful_tasks}/{total_tasks})")
    
    # 找出最慢的任务（如果有duration信息）
    tasks_with_duration = [t for t in host_tasks if t.get('duration')]
    if tasks_with_duration:
        slowest_task = max(tasks_with_duration, key=lambda x: x['duration'])
        print(f"  最慢任务: {slowest_task['task_name']} ({slowest_task['duration']}s)")
    
    # 统计任务类型
    task_types = {}
    for task in host_tasks:
        task_name = task['task_name']
        if task_name not in task_types:
            task_types[task_name] = {'count': 0, 'success': 0}
        task_types[task_name]['count'] += 1
        if task['status'] == 'ok':
            task_types[task_name]['success'] += 1
    
    print(f"  任务类型统计:")
    for task_name, stats in task_types.items():
        success_rate = (stats['success'] / stats['count']) * 100
        print(f"    {task_name}: {stats['success']}/{stats['count']} ({success_rate:.1f}%)")


if __name__ == '__main__':
    print("开始演示新的主机结果管理功能...")
    
    success = demo_host_results()
    
    if success:
        print("\n演示完成！新功能包括：")
        print("1. 每个IP维护完整的任务执行历史列表")
        print("2. 按主机获取任务结果")
        print("3. 按状态筛选任务")
        print("4. 生成主机执行摘要")
        print("5. 导出结果到JSON文件")
        print("6. 向后兼容原有的数据结构")
    else:
        print("\n演示失败，请检查错误信息。")
