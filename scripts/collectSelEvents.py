# -*- coding: utf-8 -*-
# !/usr/bin/python

import sys

reload(sys)
sys.setdefaultencoding('utf-8')

sys.path.append('../')
import subprocess
import traceback

from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from utils import *
from utils.my_mail import *
from utils.export_xlsx import XlsExporter
from resources.config import cmdb_api_host, cmdb_api_secret_key, cmdb_api_access_key
from resources.config import bmc_user
from resources.config import bmc_password

logger = get_logger(__name__, 'bmc.log')
today = datetime.today()

logfile_path = '/var/log/bmcSel.log'
AUDIT = False


class SelEvent:
    def __init__(self, hostname, ip, event_name, event_date, event_time, event_type, info):
        self.hostname = hostname
        self.ip = ip
        self.name = event_name
        self.date = event_date
        self.time = event_time
        self.type = event_type
        self.info = info
        self.datetime = datetime.strptime('{} {}'.format(self.date, self.time), '%m/%d/%Y %H:%M:%S')

    def __str__(self):
        # content = "]" + time.strip() + "@" + ip.strip() + "@" + hostname + "@" + info.strip() + "\n"
        return ']{}@{}@{}@{}'.format(
            self.datetime.strftime("%Y-%m-%d %H:%M:%S:000"),
            self.ip,
            self.hostname,
            '{} | {} | {}\n'.format(self.name.replace(' ', '').replace('/', '-'), self.type, self.info)
        )

    def __repr__(self):
        return self.__str__()


# def get_sel_list(device, user=bmc_user, password=bmc_password):
def get_sel_list(device, user, password):
    hostname = None
    outband_ip = None
    try:
        hostname = device.get('host')[0]['hostname'] if not device.get('host')[0]['hostname'] else device.get(
            'manageIP')
        outband_ip = device.get('outBandIP') if not device.get('outBandIP') else device.get('manageIP')
    except Exception as e:
        logger.error('{} get host failed'.format(json.dumps(device, ensure_ascii=False)))
        return
    if not outband_ip:
        logger.error('{} get outband_ip failed'.format(json.dumps(device, ensure_ascii=False)))
        return
    if outband_ip == '**************':
        p = subprocess.Popen(
            # args=['ipmi-sel', '--tail=10'],
            args=['ipmitool', 'sel list', 'last 10'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    else:
        # ipmitool -I lanplus -H ************** -U  -P  sel list
        # args = ['ipmi-sel', '--driver-type=LAN_2_0', '-h', device.DeviceManagementAddress, '-u', str(user), '-p',
        #         password, '--tail=10']
        args = ['ipmitool', '-I', 'lanplus', '-H', outband_ip, '-U', str(user), '-P',
                password, 'sel', 'list', 'last', '10']
        p = subprocess.Popen(
            args=args,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
    # print(' '.join(args))
    stdout, stderr = p.communicate()
    no_cipher_suite_flag = False
    cur_devices_sel_list = []
    if not stdout:
        logger.warning(
            '\n{}\n{}'.format(' '.join(args),
                              "get {} sel list failed, stdout: {}, stderr: {}".format(hostname, stdout, stderr)
                              )
        )
        if "Error in open session response message : no matching cipher suite" in stderr:
            no_cipher_suite_flag = True
        else:
            return stderr

    if no_cipher_suite_flag:
        args = ['ipmitool', '-I', 'lanplus', '-H', outband_ip, '-U', str(user), '-P',
                password, 'sel', 'list', 'last', '10', '-C', '17']

        p = subprocess.Popen(
            args=args,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
    try:
        for line in stdout.strip().split('\n')[1:]:
            raw_info = [a.strip() for a in line.split('|')]
            # raw_info = [a for a in raw_info if a]
            if not raw_info:
                continue
            if raw_info[1] == 'Pre-Init':
                continue
            id = raw_info[0]
            event_date = raw_info[1]
            event_time = raw_info[2]
            event_name = raw_info[3]
            event_type = raw_info[4]
            event_info = raw_info[5]
            cur_sel = SelEvent(
                hostname=hostname,
                ip=outband_ip,
                event_date=event_date,
                event_time=event_time,
                event_name=event_name,
                event_type=event_type,
                info=event_info
            )
            cur_devices_sel_list.append(cur_sel)
    except Exception as e:
        logger.warning('\n{}\n{}\n'.format(' '.join(args), line, '{}'.format(traceback.format_exc())))
        return '\n{}\n{}\n'.format(' '.join(args), line, '{}'.format(traceback.format_exc()))
    finally:
        return cur_devices_sel_list


if __name__ == '__main__':

    cmdb_api = CMDBApi(
        access_key=str(cmdb_api_access_key),
        secret_key=str(cmdb_api_secret_key),
        host=str(cmdb_api_host),
    )

    fields = {
            "outBandIP": True,
            "manageIP": True,
            "host.managerA": True,
            "host.managerB": True,
            "host.hostname": True,
            "host.userGroupManager": True,
            "host.osDistro": True,
            "host.ip": True,
            "status": True,
        }
    all_device_data = cmdb_api.get_all_data(
        query={
            "status": {"$eq": "运行中"},
            "userGroupManager": "服务器及存储基础设施组",
            # "host.osDistro":{
            #     "$like":"%linux%"
            # }
        },
        fields=fields,
        module="PHYSICAL_SERVER@ONEMODEL"
    )
    network_devices = cmdb_api.get_all_data(
        query={
            "status": {"$eq": "运行中"},
            "outBandIP":{'$in':[]}

        },
        fields=fields,
        module="PHYSICAL_SERVER@ONEMODEL"
    )
    all_device_data.extend(

    )



    with ThreadPoolExecutor(max_workers=48) as executor:
        result = executor.map(
            lambda p: get_sel_list(**p),
            list([{"device": d, "user": bmc_user, "password": bmc_password} for d in all_device_data])
        )
    sel_list = list(result)

    flat_sel_lists = []
    get_sel_list_failed_devices = []
    update_list = []
    stderr_dict = {}

    for i, sel_event_list in enumerate(sel_list):
        device = all_device_data[i]
        if not isinstance(sel_event_list, list):
            get_sel_list_failed_devices.append(device)
            stderr = sel_event_list
            stderr_dict[device.get('instanceId')] = stderr
            continue
        for sel_event in sel_event_list:
            if not sel_event:
                logger.error("{} no sel event".format(all_device_data[i]))
            if sel_event.datetime.month == today.month and sel_event.datetime.year == today.year:
                flat_sel_lists.append(sel_event)

    flat_sel_lists = sorted(flat_sel_lists, key=lambda x: x.datetime)

    write_sel_list = []
    with open(logfile_path, mode='r') as f_read:
        lines = f_read.readlines()
        for sel_event in flat_sel_lists[::-1]:
            if sel_event.datetime.date() == datetime.today().date() and str(sel_event) not in lines:
                # if str(sel_event) not in lines:
                write_sel_list.append(sel_event)
            else:
                break
    write_sel_list = write_sel_list[::-1]

    with open(logfile_path, mode='a+') as fin:
        for sel_event in write_sel_list:
            fin.write(str(sel_event))

    if AUDIT:
        def get_linux(device):
            os_flag = False
            ip_flag = False
            if not device.get('host'):
                return False
            osDistro = device.get('host')[0].get('osDistro')
            ip = device.get('host')[0].get('ip')
            for _ in ['red', 'centos', 'kylin']:
                if osDistro and _ in osDistro.lower():
                    os_flag = True
                if '10.142.' not in ip:
                    ip_flag = True
            return os_flag and ip_flag


        general_linux_physic_device = filter(get_linux, all_device_data)
        get_sel_list_failed_linux_physic_devices = filter(get_linux, get_sel_list_failed_devices)

        DEVICES_VARS = collections.OrderedDict()
        DEVICES_VARS['主机名'] = lambda x: x.get('host', [{}, ])[0].get('hostname', 'CMDB未查询到') if len(
            x.get('host', [{}, ])) > 0 else 'CMDB未查询到'
        DEVICES_VARS['管理员A'] = lambda x: x.get('host', [{}, ])[0].get('managerA', 'CMDB未查询到') if len(
            x.get('host', [{}, ])) > 0 else 'CMDB未查询到'
        DEVICES_VARS['管理员B'] = lambda x: x.get('host', [{}, ])[0].get('managerB', 'CMDB未查询到') if len(
            x.get('host', [{}, ])) > 0 else 'CMDB未查询到'
        DEVICES_VARS['主机OS类型'] = lambda x: x.get('host', [{}, ])[0].get('osDistro', 'CMDB未查询到') if len(
            x.get('host', [{}, ])) > 0 else 'CMDB未查询到'
        DEVICES_VARS['设备在线'] = lambda x: x.get('status', 'CMDB未查询到')
        DEVICES_VARS['主机IP'] = lambda x: x.get('host', [{}, ])[0].get('ip', 'CMDB未查询到') if len(
            x.get('host', [{}, ])) > 0 else 'CMDB未查询到'
        DEVICES_VARS['部门组别'] = lambda x: x.get('host', [{}, ])[0].get('userGroupManager', 'CMDB未查询到') if len(
            x.get('host', [{}, ])) > 0 else 'CMDB未查询到'
        DEVICES_VARS['带外IP'] = lambda x: x.get('manageIP', 'CMDB未查询到')
        # DEVICES_VARS['服务器版本'] = lambda x: x.__dict__.get('modle', 'CMDB未查询到')
        DEVICES_VARS['错误信息'] = lambda x: stderr_dict.get(x.get('instanceId'))
        xlsx_path = '/tmp/ipmi_failed_devices.xlsx'
        xlsx_e = XlsExporter(xlsx_path)
        xlsx_e.write_devices('所有物理服务器', get_sel_list_failed_devices, row_vars=DEVICES_VARS)
        xlsx_e.write_devices('所有内网Linux物理服务器', get_sel_list_failed_linux_physic_devices, row_vars=DEVICES_VARS)
        xlsx_e.save_workbook()

        # manager = set([device for device in get_sel_list_failed_devices])
        # count = {m: 0 for m in manager}
        # for d in get_sel_list_failed_devices:
        #     count[d.ManagerA] = count[d.ManagerA] + 1
        # from six import u
        # from utils.HTMLTable import HTMLTable
        #
        # table = HTMLTable(caption='人数统计', value_formatter=u)
        # table.append_header_rows([['管理员', '名下未成功数量']])
        # data_rows = [[m, count[m]] for m in sorted(manager)]
        # table.append_data_rows(data_rows)
        # table.caption.set_style({'font-size': '15px'})
        # table.set_style({
        #     'border-collapse': 'collapse',
        #     'word-break': 'keep-all',
        #     'white-space': 'nowrap',
        #     'font-size': '14px'
        # })
        # table.set_header_row_style({'color': '#fff', 'background-color': '#48a6fb', 'font-size': '18px'})
        # table.set_header_cell_style({'padding': '15px'})

        message = []
        ans = "管理物理服务器数量 {}，IPMI纳管失败数量{},占比{}%".format(
            len(all_device_data), len(get_sel_list_failed_devices),
            round(len(get_sel_list_failed_devices) * 1.000000 / (len(all_device_data)) * 100, 2)
        )

        ans2 = "办公内网管理通用Linux物理服务器数量 {}，IPMI纳管失败数量{},占比{}%".format(
            len(general_linux_physic_device), len(get_sel_list_failed_linux_physic_devices),
            round(len(get_sel_list_failed_linux_physic_devices) * 1.000000 / (len(general_linux_physic_device)) * 100,
                  2)
        )

        message.append(mail_file(xlsx_path, 'xlsx', 'ipmi失败机器.xlsx'))
        message.append(mail_html(ans + '\n' + ans2))
        from resources.config import mail_user, mail_host, mail_password

        send_mail(
            ['<EMAIL>', ], 'ipmi稽核邮件', contents=message,
            mail_user=mail_user, mail_host=mail_host, mail_password=mail_password
        )
