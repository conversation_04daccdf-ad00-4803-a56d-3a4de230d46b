# -*- coding: utf-8 -*-
# !/usr/bin/python

import os
import sys
import xlrd
import openpyxl
import pyexcel
from datetime import datetime

reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
baseline_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(baseline_path))
utils_path = root_path + '/utils'           # ./utils
#resource_path = root_path + '/resources'    # ./resources

sys.path.append(root_path)
sys.path.append(utils_path)
sys.path.append(public_resources)

from utils.my_mail import *
from utils import CMDBApi
from utils import my_print
from item import *

from config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host

cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

def gen_dell_dict():
    book = xlrd.open_workbook(filename='./dell.xlsx')
    table = book.sheets()[0]
    row_count = table.nrows

    dell_dict = {}
    for row in range(1, row_count):
        sn = table.cell(row, 1).value.strip().upper()
        expiration = xlrd.xldate_as_datetime(table.cell(row, 3).value, 0) #<type 'datetime.datetime'>
        expiration = expiration.strftime('%Y-%m-%d')
        dell_dict[sn] = expiration
    
    return dell_dict

def gen_inspur_dict():
    book = xlrd.open_workbook(filename='./inspur.xls')
    table = book.sheets()[0]
    row_count = table.nrows

    inspur_dict = {}
    for row in range(1, row_count):
        sn = table.cell(row, 0).value.strip().upper()
        expiration = str(table.cell(row, 3).value)
        #expiration = datetime.strptime(expiration, "%Y-%m-%d") #<type 'datetime.datetime'>
        inspur_dict[sn] = expiration
    
    return inspur_dict

def gen_dev_dict():
    devices = cmdb_api.get_physical_server_data(
        query = {
            "userGroupManager": "服务器及存储基础设施组",
        }
    )

    dict = {}
    for dev in devices:
        name = dev.get('name')
        if not name:
            continue
        dict[name] = dev
    
    return dict


def fill_info(dell_date_by_sn, inspur_date_by_sn, cmdb_dev_by_name):
    pyexcel.save_book_as(file_name='raw_list.xls', dest_file_name='raw_list.xlsx')
    book = openpyxl.load_workbook('raw_list.xlsx')
    sheet = book.active
    dict = {}
    for row in range(2, sheet.max_row + 1):
        name = sheet.cell(column=4, row=row).value.strip()
        dev = cmdb_dev_by_name.get(name)
        sheet.cell(column=9, row=row).value  = dev.get('cpuModel')
        sheet.cell(column=10, row=row).value = dev.get('memorySize')
        sheet.cell(column=11, row=row).value = dev.get('diskSize')
        
        sn = sheet.cell(column=1, row=row).value
        if not sn:
            continue

        sn = sn.strip().upper()

        brand = sheet.cell(column=7, row=row).value
        
        if brand:
            brand = brand.strip().upper()
        if brand == 'DELL':
            sheet.cell(column=3, row=row).value = dell_date_by_sn.get(sn)
        elif brand == 'INSPUR':
            sheet.cell(column=3, row=row).value = inspur_date_by_sn.get(sn)

        if not sheet.cell(column=3, row=row).value and sheet.cell(column=2, row=row).value:
            purchase_date = sheet.cell(column=2, row=row).value.strip()
            purchase_year = purchase_date.split('-', 1)[0]
            expire_year = str(int(purchase_year) + 5)
            expire_date = expire_year + '-' + purchase_date.split('-', 1)[1]
            sheet.cell(column=3, row=row).value = expire_date
    
    book.save('/tmp/post.xlsx')

    return dict

if __name__ == '__main__':

    dell_date_by_sn = gen_dell_dict()
    inspur_date_by_sn = gen_inspur_dict()
    cmdb_dev_by_name = gen_dev_dict()

    fill_info(dell_date_by_sn, inspur_date_by_sn, cmdb_dev_by_name)