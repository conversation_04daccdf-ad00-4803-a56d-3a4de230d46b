# -*- coding: utf-8 -*-
# !/usr/bin/python

import os
import sys
import xlwt
import openpyxl
from datetime import datetime

reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
baseline_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(baseline_path))
utils_path = root_path + '/utils'           # ./utils
#resource_path = root_path + '/resources'    # ./resources

sys.path.append(root_path)
sys.path.append(utils_path)
sys.path.append(public_resources)

from utils.my_mail import *
from utils import CMDBApi
from utils import my_print
from item import *

from config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host

cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

class WarrantyInfoBook():
    def __init__(self):
        self.workbook = xlwt.Workbook(encoding='utf-8')
        self.sheet = WarrantyInfoSheet(self.workbook, '基础设施组物理服务器信息')

    def aggregate(self, sn, purchase_date, hostname, status, os_ver, brand, model, ip, location, manager_a, manager_b):
        self.sheet.result_write(sn, purchase_date, hostname, status, os_ver, brand, model, ip, location, manager_a, manager_b)

    def dump(self):
        self.workbook.save('/tmp/jcssz_server_info.xls')

class WarrantyInfoSheet():
    def __init__(self, workbook, sheetname):
        self.row = 0
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 3500
        self.worksheet.col(1).width = 3500
        self.worksheet.col(2).width = 7000
        self.worksheet.col(3).width = 2500
        self.worksheet.col(4).width = 10000
        self.worksheet.col(5).width = 3000
        self.worksheet.col(6).width = 6000
        self.worksheet.col(7).width = 4000
        self.worksheet.col(8).width = 10000
        self.worksheet.col(9).width = 2500
        self.worksheet.col(10).width = 2500

        #style settings
        self.style = xlwt.XFStyle()
        alignment = xlwt.Alignment()
        alignment.horz = 0x01
        self.style.alignment = alignment

        self.result_write('序列号', '购置日期', '主机名', '状态', '操作系统', '品牌', '型号', '管理IP', '位置', '管理员A', '管理员B')

    def result_write(self, sn, purchase_date, hostname, status, os_ver, brand, model, ip, location, manager_a, manager_b):
        item_list = [sn, purchase_date, hostname, status, os_ver, brand, model, ip, location, manager_a, manager_b]
        column = 0
        for item in item_list:
            self.worksheet.write(self.row, column, item)
            column += 1

        self.row += 1

def gen_sn_dict():
    book = openpyxl.load_workbook('assets.xlsx')
    sheet = book.active
    dict = {}
    for row in range(2, sheet.max_row + 1):
        sn = sheet.cell(column=8, row=row).value.strip().upper()
        if not sn:
            continue

        date = sheet.cell(column=16, row=row).value.strip()
        dict[sn] = date

    return dict

def get_host_os_ver(host_dict, hostname):
    dev = host_dict.get(hostname)
    if dev:
        return dev.get('osDistro')
    else:
        return ''

def get_host_location(host_dict, hostname):
    dev = host_dict.get(hostname)
    if dev:        
        location = ''
        physicalServer = dev.get('physicalServer')
        if physicalServer:
            rack = physicalServer[0].get('rack')
            if rack:
                location = rack[0].get('name')
        return location
    else:
        return ''

def get_host_manager_a(host_dict, hostname):
    dev = host_dict.get(hostname)
    if dev:
        return dev.get('managerA')
    else:
        return ''

def get_host_manager_b(host_dict, hostname):
    dev = host_dict.get(hostname)
    if dev:
        return dev.get('managerB')
    else:
        return ''

def get_host_ip(host_dict, hostname):
    dev = host_dict.get(hostname)
    if dev:
        return dev.get('ip')
    else:
        return ''

def gen_host_dict():
    hosts = cmdb_api.get_host_data(
        query = {
            "userGroupManager": "服务器及存储基础设施组",
            "item": "物理服务器"
        }
    )
    host_dict = {}
    for host in hosts:
        hostname = host.get('hostname')
        if hostname:
            host_dict[hostname] = host

    return host_dict

if __name__ == '__main__':

    sn_dict = gen_sn_dict()
    host_dict = gen_host_dict()
    
    warranty_book = WarrantyInfoBook()

    physical_dev = cmdb_api.get_physical_server_data(
        query = {
            "userGroupManager": "服务器及存储基础设施组",
        }
    )

    for dev in physical_dev:
        sn       = dev.get('sn')
        hostname = dev.get('name')
        status   = dev.get('status')
        brand    = dev.get('brand')
        model    = dev.get('mdl')
        ip       = dev.get('manageIP')
        if not ip:
            ip = get_host_ip(host_dict, hostname)
        os_ver   = get_host_os_ver(host_dict, hostname)
        location = get_host_location(host_dict, hostname)
        managerA = get_host_manager_a(host_dict, hostname)
        managerB = get_host_manager_b(host_dict, hostname)

        if sn:
            purchase_date = sn_dict.get(sn)

        warranty_book.aggregate(sn, purchase_date, hostname, status, os_ver, brand, model, ip, location, managerA, managerB)

    warranty_book.dump()