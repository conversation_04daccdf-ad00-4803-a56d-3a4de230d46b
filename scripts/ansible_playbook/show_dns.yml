---
- name: check baseline
  hosts: all
  gather_facts: no
  vars:
    - default_user: "roota"
  vars_files:
    - /home/<USER>/ChenL/Utils/vault/my_vault
  tasks:
    - name: Find and set connection
      set_fact:
        select_connection: >
          {{
            (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list | first)
            if (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list) | length > 0
            else vars[inventory_hostname].connections[0]
          }}
    - name: set fact from connection
      set_fact:
        ansible_user: "{{ select_connection.user }}"
        ansible_ssh_pass: "{{ select_connection.password }}"

    - name: show DNS
      shell: cat /etc/resolv.conf
