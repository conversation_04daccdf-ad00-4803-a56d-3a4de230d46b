---
- name: check baseline
  hosts: all
  gather_facts: no
  vars:
    - default_user: "roota"
  vars_files:
    - /home/<USER>/ChenL/Utils/vault/my_vault
  tasks:
    - name: Find and set connection
      set_fact:
        select_connection: >
          {{
            (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list | first)
            if (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list) | length > 0
            else vars[inventory_hostname].connections[0]
          }}
    - name: set fact from connection
      set_fact:
        ansible_user: "{{ select_connection.user }}"
        ansible_ssh_pass: "{{ select_connection.password }}"


    - name: remove baseline check files
      shell: rm /tmp/baseline_check_* -rf
    - name: download and unarchive
      unarchive:
        src: http://yum.sse.org.cn/baseline/check/baseline_check_files.tgz
        dest: /tmp/
        remote_src: yes
    - name: run check scripts
      shell: cd /tmp/baseline_check_files/ && python ./baseline_check.py
    - name: show answer
      shell: cat /tmp/baseline_check_files/res.json
