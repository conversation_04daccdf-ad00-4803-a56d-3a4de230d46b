# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys, os
reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
hids_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(hids_path)) #../
utils_path = root_path + '/utils' #../utils

sys.path.append(public_resources)
sys.path.append(root_path)
sys.path.append(utils_path)

import hashlib
import json
import time

import requests
import sqlalchemy
import xlwt

from db_connection import SqliteMemoryConnection, MysqlConnection
from item import Device_MEM, HIDSBlackList, HIDSExemptionListNew, HIDSExemptionList
from utils import devices_to_html_table
from utils import CMDBApi
from utils import my_print

from resources.config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host
cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

jcssz_db_connection = MysqlConnection()

# get all the running devices
def get_all_devices():
    jcssz_devices = cmdb_api.get_all_data(
        query = {
            #"status": {"$eq": "运行中"},
            "userGroupManager": "服务器及存储基础设施组"
            #"$or": [
            #    {"osDistro": {"$like": '%red%'}},
            #    {"osDistro": {"$like": '%centos%'}},
            #    {"osDistro": {"$like": '%Kylin%'}}
            #],
        },
        fields={
            "instanceID": True,
            "hostname": True,
            "ip": True,
            "item": True,
            "model": True,
            "status": True,
            "osDistro": True,
            "managerA": True,
            "managerB": True,
            "userGroupManager": True,
            "physicalServer.rack.name": True
        }
    )
    return jcssz_devices

def jcssz_db_commit():
    jcssz_db_connection.session.commit()

def single_dev_migrate(dev):
    # cmdb -> blacklist mysql
    new_entry = HIDSExemptionListNew()
    new_entry.DeviceInstanceID = dev.get('instanceId')
    new_entry.DeviceHostName = dev.get('hostname')
    new_entry.DeviceIPAddress = dev.get('ip')
    new_entry.DeviceCSOSVersion = dev.get('osDistro') if dev.get('osDistro') else 'n/a'
    new_entry.DeviceCIStatus = dev.get('status')
    new_entry.DeviceManagerGroup = dev.get('userGroupManager')
    new_entry.DeviceManagerA = dev.get('managerA')
    new_entry.DeviceType = dev.get('item')
    new_entry.DeviceShortDescription = dev.get('model') if dev.get('model') else 'n/a'
    new_entry.Reason = '从原豁免名单迁移'
    if dev['physicalServer'] and dev['physicalServer'][0]['rack']:
        new_entry.DeviceCabinet = dev['physicalServer'][0]['rack'][0]['name']
    else:
        new_entry.DeviceCabinet = 'n/a'
    jcssz_db_connection.session.merge(new_entry)

def tbl_migrate(jcssz_devices):
    exemption_hostnames = list([a[0] for a in jcssz_db_connection.session.query(HIDSBlackList.DeviceHostName).filter(
        #HIDSBlackList.DeviceCIStatus == u'运行中',
        HIDSBlackList.DeviceManagerGroup == u'服务器及存储基础设施组'
    ).all()])

    for dev in jcssz_devices:
        if dev['hostname'] in exemption_hostnames:
            single_dev_migrate(dev)
    
    jcssz_db_commit()

def get_reason_by_hostname(hostname):
    jcssz_db_connection.session.query(HIDSExemptionList).filter_by(HostName=hostname).first()

if __name__ == '__main__':

    hosts = jcssz_db_connection.session.query(HIDSExemptionList).filter_by(ManagerGroup=u'服务器及存储基础设施组').all()

    #reason_by_host = {}
    for host in hosts:
        #reason_by_host[host.HostName] = host.reason
        entry = jcssz_db_connection.session.query(HIDSExemptionListNew).filter_by(DeviceHostName=host.HostName).first()
        if entry:
            entry.Reason = host.reason

    jcssz_db_connection.session.commit()
