<mxfile host="65bd71144e">
    <diagram id="9uh6MeMpFHk2LJBaWhQq" name="第 1 页">
        <mxGraphModel dx="1117" dy="792" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="12" value="&lt;h1&gt;HIDS稽核脚本工作流程&lt;/h1&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="40" y="40" width="510" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="CMDB" style="html=1;fontSize=12;align=center;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="240" y="130" width="80" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="&lt;span style=&quot;font-size: 12px;&quot;&gt;基础设施组数据库，豁免清单表&lt;/span&gt;&lt;br style=&quot;border-color: var(--border-color); font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;库名：jcssz_mon&lt;/span&gt;&lt;br style=&quot;border-color: var(--border-color); font-size: 12px;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;表名：tbl_hids_exemption_list_new&lt;/span&gt;" style="html=1;fontSize=18;align=left;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="400" y="100" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="HIDS数据库&lt;br style=&quot;font-size: 12px;&quot;&gt;（qingteng）" style="html=1;fontSize=12;align=left;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="680" y="120" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="34">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="801" as="sourcePoint"/>
                        <mxPoint x="80" y="180" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="500" y="801" as="sourcePoint"/>
                        <mxPoint x="499.5" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="720" y="801" as="sourcePoint"/>
                        <mxPoint x="719.5" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="280" y="240" as="sourcePoint"/>
                        <mxPoint x="500" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="1.基于CMDB数据更新豁免清单数据库中的主机信息&lt;br&gt;（update_existing_dev）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="320" y="210" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="python进程" style="html=1;fontSize=12;align=left;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="80" y="131" width="80" height="29" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="" style="endArrow=none;dashed=1;html=1;fontSize=18;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="22">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="280" y="801" as="sourcePoint"/>
                        <mxPoint x="280" y="159" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="320" as="sourcePoint"/>
                        <mxPoint x="500" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="2.基于-p选项指定的excel文件在豁免清单数据库新增主机（import_exemption_from_excel）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="185" y="289" width="190" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="720" y="401" as="sourcePoint"/>
                        <mxPoint x="120" y="401" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" value="3.从qingteng数据库获取主机HIDS安装情况&lt;br&gt;（get_qingteng_info）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="330" y="371" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="521" as="sourcePoint"/>
                        <mxPoint x="120" y="441" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="4.对qingteng和CMDB数据对进行清洗和主机分类&lt;br&gt;（classify_devices）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="130" y="451" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="500" y="561" as="sourcePoint"/>
                        <mxPoint x="120" y="561" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="5.获取更新后的豁免清单" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="210" y="541" width="140" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="681" as="sourcePoint"/>
                        <mxPoint x="120" y="601" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="6.基于步骤4和步骤5结果生成统计数据&lt;br&gt;（generate_stats）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="130" y="611" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="endArrow=none;html=1;fontSize=24;strokeWidth=5;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="801" as="sourcePoint"/>
                        <mxPoint x="120" y="721" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="7.基于步骤4和步骤5结果生成统计数据&lt;br&gt;（send_stats）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="130" y="731" width="140" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>