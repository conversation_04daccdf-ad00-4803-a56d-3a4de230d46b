# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys, os
reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
hids_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(hids_path)) #../
utils_path = root_path + '/utils' #../utils

sys.path.append(public_resources)
sys.path.append(root_path)
sys.path.append(utils_path)

import hashlib
import json
import time

import argparse
import requests
import xlwt
import xlrd

from db_connection import MysqlConnection
from item import HIDSExemptionListNew
from utils.my_mail import *
from utils import devices_to_html_table
from utils import CMDBApi
from utils import my_print

from config import cmdb_api_access_key, cmdb_api_secret_key, cmdb_api_host
cmdb_api = CMDBApi(
    access_key = cmdb_api_access_key,
    secret_key = cmdb_api_secret_key,
    host = cmdb_api_host
)

jcssz_db_connection = MysqlConnection()

#qingteng utils
class QingTengHelper:
    def __init__(self, address='qingtengurl', user='qingtenguser', password='qingtengpassword'):
        self.address = address
        self.user = user
        self.password = password
        self._auth()

    def _auth(self):
        header = {"Content-Type": "application/json"}
        data = {"username": self.user, "password": self.password}
        re = requests.post(url=self.address + '/v1/api/auth', data=json.dumps(data), headers=header)
        if not re.json():
            raise Exception('Unauthorized!')
        self.signKey = re.json().get('data').get('signKey')
        self.jwt = re.json().get('data').get('jwt')
        self.comId = re.json().get('data').get('comId')

    def request(self, api, payload, method='get'):
        ts = int(time.time())
        if not payload:
            to_sign = self.comId + str(ts) + self.signKey
        else:
            info = ""
            if method == 'get':
                keys = sorted(payload.keys())
                for key in keys:
                    info = info + key + str(payload.get(key))

            elif method in ('post', 'put', 'delete'):
                info = json.dumps(payload)
            to_sign = self.comId + info + str(ts) + self.signKey
        #print(to_sign)
        sign = hashlib.sha1(to_sign).hexdigest()
        header = {
            "Content-Type": "application/json",
            "comId": self.comId,
            "timestamp": str(ts),
            "sign": sign,
            "Authorization": "Bearer " + self.jwt
        }
        re = requests.request(method=method, url=self.address + api, data=json.dumps(payload), headers=header)
        return re

    def get_linux_devices(self):
        all_rows = []
        init_re = self.request(api='/external/api/assets/host/linux', payload=None, method='get')
        total_size = init_re.json()['total']
        all_rows.extend(init_re.json()['rows'])
        page = 1
        for batch in range(50, total_size, 50):
            cur_re = self.request(api='/external/api/assets/host/linux?page={}&size=50'.format(page),
                                  payload={'page': page, 'size': 50}, method='get')
            all_rows.extend(cur_re.json()['rows'])
            page += 1
        return all_rows

    def get_win_devices(self):
        all_rows = []
        init_re = self.request(api='/external/api/assets/host/win', payload=None, method='get')
        total_size = init_re.json()['total']
        all_rows.extend(init_re.json()['rows'])
        page = 1
        for batch in range(50, total_size, 50):
            cur_re = self.request(api='/external/api/assets/host/win?page={}&size=50'.format(page),
                                  payload={'page': page, 'size': 50}, method='get')
            all_rows.extend(cur_re.json()['rows'])
            page += 1
        return all_rows

class HIDS_Sheet():
    def __init__(self, workbook, sheetname):
        #sheet settings
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 4500
        self.worksheet.col(1).width = 2000
        self.worksheet.col(2).width = 2000
        self.worksheet.col(3).width = 9000
        self.worksheet.col(4).width = 2000
        self.worksheet.col(5).width = 4500
        self.worksheet.col(6).width = 5000

        #style settings
        # self.style = xlwt.XFStyle()
        # alignment = xlwt.Alignment()
        # alignment.horz = 0x01
        #self.style.alignment = alignment

        self.row = 0
        title_dict = {
            'hostname': '主机名',
            'managerA': '管理员A',
            'managerB': '管理员B',
            'osDistro': '主机OS类型',
            'status'  : '设备状态',
            'ip'      : '主机IP',
            'userGroupManager': '部门组别'
        }
        self.result_write(title_dict)        

    def result_write(self, dev_dict):
        self.worksheet.write(self.row, 0, dev_dict['hostname'])
        self.worksheet.write(self.row, 1, dev_dict['managerA'])
        self.worksheet.write(self.row, 2, dev_dict['managerB'])
        #some of the dicts don't have this key
        try:
            self.worksheet.write(self.row, 3, dev_dict['osDistro'])
        except:
            self.worksheet.write(self.row, 3, ' ')
        self.worksheet.write(self.row, 4, dev_dict['status'])
        self.worksheet.write(self.row, 5, dev_dict['ip'])
        self.worksheet.write(self.row, 6, dev_dict['userGroupManager'])
        self.row += 1

class Exemption_Sheet():
    def __init__(self, workbook, sheetname):
        #sheet settings
        self.worksheet = workbook.add_sheet(sheetname=sheetname)
        self.worksheet.col(0).width = 4500
        self.worksheet.col(1).width = 2000
        self.worksheet.col(2).width = 2000
        self.worksheet.col(3).width = 9000
        self.worksheet.col(4).width = 2000
        self.worksheet.col(5).width = 4500
        self.worksheet.col(6).width = 6000
        self.worksheet.col(7).width = 10000

        #style settings
        # self.style = xlwt.XFStyle()
        # alignment = xlwt.Alignment()
        # alignment.horz = 0x01
        #self.style.alignment = alignment

        self.row = 0
        title_dict = {
            'hostname': '主机名',
            'managerA': '管理员A',
            'managerB': '管理员B',
            'osDistro': '主机OS类型',
            'status'  : '设备状态',
            'ip'      : '主机IP',
            'userGroupManager': '部门组别'
        }
        self.result_write(title_dict, '豁免理由')        

    def result_write(self, dev_dict, reason):
        self.worksheet.write(self.row, 0, dev_dict['hostname'])
        self.worksheet.write(self.row, 1, dev_dict['managerA'])
        self.worksheet.write(self.row, 2, dev_dict['managerB'])
        #some of the dicts don't have this key
        try:
            self.worksheet.write(self.row, 3, dev_dict['osDistro'])
        except:
            self.worksheet.write(self.row, 3, ' ')
        self.worksheet.write(self.row, 4, dev_dict['status'])
        self.worksheet.write(self.row, 5, dev_dict['ip'])
        self.worksheet.write(self.row, 6, dev_dict['userGroupManager'])
        if not reason:
            reason = '封闭式系统'
        self.worksheet.write(self.row, 7, reason)
        self.row += 1


"""
online/offline info from qingteng
"""
def get_qingteng_info():
    qth = QingTengHelper()
    all_linux = qth.get_linux_devices()
    all_win = qth.get_win_devices()

    # 一般这种情况是主机有些变化，例如cpu\内存、磁盘之类的变动，导致id变了，原agent离线新agent重新上线
    # 所以对于在线，离线，需要去重

    online_hostname_list = [a.get('hostname').split('.novalocal')[0] for a in all_linux + all_win if
                        a['onlineStatus'] == 1]
    offline_hostname_list = [a.get('hostname').split('.novalocal')[0] for a in all_linux + all_win if
                            a['onlineStatus'] == 0]
    online_display_ip = [a.get('displayIp') for a in all_linux + all_win if a['onlineStatus'] == 1]
    offline_display_ip = [a.get('displayIp') for a in all_linux + all_win if a['onlineStatus'] == 0]
    true_online_hostname_set = set(online_hostname_list)
    true_offline_hostname_set = set(offline_hostname_list) - set(online_hostname_list)
    true_online_display_ip_set = set(online_display_ip)
    true_offline_display_ip_set = set(offline_display_ip) - set(online_display_ip)

    return true_online_hostname_set, true_offline_hostname_set, true_online_display_ip_set, true_offline_display_ip_set

"""
classify the running devices based on online/offline info from qingteng
"""
def classify_devices(jcssz_devices, qingteng_info):
    true_online_hostname_set = qingteng_info[0]
    true_offline_hostname_set = qingteng_info[1]
    true_online_display_ip_set = qingteng_info[2]
    true_offline_display_ip_set = qingteng_info[3]

    hids_online_devices = []
    hids_offline_devices = []
    raw_no_cover_devices = []

    for device in jcssz_devices:
        hostname_online_flag = False
        hostname_offline_flag = False
        ip_online_flag = False
        ip_offline_flag = False

        # 1.check hostname
        if device['hostname'] in true_online_hostname_set:
            hostname_online_flag = True
        if device['hostname'] in true_offline_hostname_set:
            hostname_offline_flag = True
        # 2.check ip
        if not device['ip']:
            continue
        if device['ip'] in true_online_display_ip_set:
            ip_online_flag = True
        if device['ip'] in true_offline_display_ip_set:
            ip_offline_flag = True
        
        # 两个如果在一个，那就当在线
        if hostname_online_flag or ip_online_flag:
            hids_online_devices.append(device)
        # 同理，如果在离线
        elif hostname_offline_flag or ip_offline_flag:
            hids_offline_devices.append(device)
        # 如果都不在，那就没覆盖到
        elif not hostname_online_flag and not ip_online_flag:
            raw_no_cover_devices.append(device)

    return hids_online_devices, hids_offline_devices, raw_no_cover_devices

def single_dev_merge(dev, reason):
    # cmdb -> blacklist mysql
    new_entry = HIDSExemptionListNew()
    new_entry.DeviceInstanceID = dev.get('instanceId')
    new_entry.DeviceHostName = dev.get('hostname')
    new_entry.DeviceIPAddress = dev.get('ip')
    new_entry.DeviceCSOSVersion = dev.get('osDistro') if dev.get('osDistro') else 'n/a'
    new_entry.DeviceCIStatus = dev.get('status')
    new_entry.DeviceManagerGroup = dev.get('userGroupManager')
    new_entry.DeviceManagerA = dev.get('managerA')
    new_entry.DeviceType = dev.get('item')
    new_entry.DeviceShortDescription = dev.get('model') if dev.get('model') else 'n/a'
    new_entry.Reason = reason
    if dev['physicalServer'] and dev['physicalServer'][0]['rack']:
        new_entry.DeviceCabinet = dev['physicalServer'][0]['rack'][0]['name']
    else:
        new_entry.DeviceCabinet = 'n/a'
    jcssz_db_connection.session.merge(new_entry)

def single_dev_update(dev):
    if not dev:
        return
    
    entry = jcssz_db_connection.session.query(HIDSExemptionListNew).filter_by(DeviceHostName=dev['hostname']).first()
    entry.DeviceInstanceID = dev.get('instanceId')
    entry.DeviceHostName = dev.get('hostname')
    entry.DeviceIPAddress = dev.get('ip')
    entry.DeviceCSOSVersion = dev.get('osDistro') if dev.get('osDistro') else 'n/a'
    entry.DeviceCIStatus = dev.get('status')
    entry.DeviceManagerGroup = dev.get('userGroupManager')
    entry.DeviceManagerA = dev.get('managerA')
    entry.DeviceType = dev.get('item')
    entry.DeviceShortDescription = dev.get('model') if dev.get('model') else 'n/a'
    if dev['physicalServer'] and dev['physicalServer'][0]['rack']:
        entry.DeviceCabinet = dev['physicalServer'][0]['rack'][0]['name']
    else:
        entry.DeviceCabinet = 'n/a'

def jcssz_db_commit():
    jcssz_db_connection.session.commit()

def update_existing_dev(dev_by_hostname):
    exemption_hosts = list([a[0] for a in jcssz_db_connection.session.query(HIDSExemptionListNew.DeviceHostName).filter(
        #HIDSBlackListNew.DeviceCIStatus == u'运行中',
        HIDSExemptionListNew.DeviceManagerGroup == u'服务器及存储基础设施组'
    ).all()])

    for hostname in exemption_hosts:
        dev = dev_by_hostname.get(hostname)
        single_dev_update(dev)

def import_exemption_from_excel(dev_by_hostname, path):
    if not path:
        return
        
    exemption_file = xlrd.open_workbook(path)
    exemption_tbl = exemption_file.sheet_by_name('未覆盖')
    for row in range(1, exemption_tbl.nrows):
        is_exempted = exemption_tbl.cell(row, 7).value
        if is_exempted == '是':
            hostname = exemption_tbl.cell(row, 0).value
            reason = exemption_tbl.cell(row, 8).value
            dev = dev_by_hostname.get(hostname)
            if dev:
                single_dev_merge(dev, reason)
            else:
                print('hostname {} not found!'.format(hostname))

def update_exemption_db(args):
    dev_by_hostname = gen_dev_dict()
    update_existing_dev(dev_by_hostname)
    import_exemption_from_excel(dev_by_hostname, args.path)
    jcssz_db_commit()

# get devices info from cmdb
def get_cmdb_devices(running_only = True, linux_only = False):

    query = dict()
    query['userGroupManager'] = "服务器及存储基础设施组"
    if running_only:
        query['status'] = {"$eq": "运行中"}

    if linux_only:
        query['$or'] = [
           {"osDistro": {"$like": '%red%'}},
           {"osDistro": {"$like": '%centos%'}},
           {"osDistro": {"$like": '%Kylin%'}}
        ]
    
    fields={
        "instanceID": True,
        "hostname": True,
        "ip": True,
        "item": True,
        "model": True,
        "status": True,
        "osDistro": True,
        "managerA": True,
        "managerB": True,
        "userGroupManager": True,
        "physicalServer.rack.name": True
    }

    devices = cmdb_api.get_host_data(query, fields)

    return devices

def gen_dev_dict():
    devices = get_cmdb_devices(running_only=False)
    dict = {}
    for dev in devices:
        hostname = dev.get('hostname')
        if hostname:
            dict[hostname] = dev
    return dict

def generate_stats(jcssz_devices, hids_online_devices, hids_offline_devices, raw_no_cover_devices):
    jcssz_devices_instance_set   = set([device['hostname'] for device in jcssz_devices])
    raw_exemption_device_instance_set = set([a[0] for a in jcssz_db_connection.session.query(HIDSExemptionListNew.DeviceHostName).filter(
        HIDSExemptionListNew.DeviceCIStatus == u'运行中',
        HIDSExemptionListNew.DeviceManagerGroup == u'服务器及存储基础设施组'
    ).all()])    
    exemption_device_instance_set = raw_exemption_device_instance_set & jcssz_devices_instance_set

    if len(exemption_device_instance_set):
        print('豁免清单中，但不在CMDB中的主机')
        print(raw_exemption_device_instance_set - exemption_device_instance_set)

    online_devices_instance_set  = set([device['hostname'] for device in hids_online_devices])
    offline_devices_instance_set = set([device['hostname'] for device in hids_offline_devices])
    installed_devices_instance_set = online_devices_instance_set | offline_devices_instance_set    
    no_cover_devices_instance_set= set([device['hostname'] for device in raw_no_cover_devices]) - exemption_device_instance_set
    #error_devices_instance_set = online_devices_instance_set & offline_devices_instance_set
    # 在豁免名单但是检测出已安装的机器不计入豁免名单
    unexpected_installed_instance_set = installed_devices_instance_set & exemption_device_instance_set
    exemption_device_instance_set = exemption_device_instance_set - unexpected_installed_instance_set

    if len(unexpected_installed_instance_set):
        print('豁免清单中，但安装了HIDS的主机：')
        print(unexpected_installed_instance_set)

    # output stats
    total_count = len(jcssz_devices_instance_set)
    exemption_list_count = len(exemption_device_instance_set)

    expected_install_count = total_count - exemption_list_count
    installed_count = len(online_devices_instance_set | offline_devices_instance_set)
    not_covered_count = expected_install_count - installed_count
    hids_coverage = round(float(installed_count)/expected_install_count*100, 2)

    online_count = len(online_devices_instance_set)
    offline_count = len(offline_devices_instance_set)
    online_ratio = round(float(online_count)/(online_count+offline_count)*100, 2)

    summary = "管理主机数量 {}；豁免主机数量 {}；应安装主机数量 {}；" \
          "已安装数量 {}；未覆盖主机数量 {}；HIDS覆盖率 {}%；" \
          "已运行HIDS数量 {}；未运行数量 {}；在线率 {}%".format(
        total_count, exemption_list_count, expected_install_count,
        installed_count, not_covered_count, hids_coverage,
        online_count, offline_count, online_ratio)

    no_cover_devices = []
    offline_devices = []
    exemption_devices = []
    for dev in jcssz_devices:
        if dev['hostname'] in no_cover_devices_instance_set:
            no_cover_devices.append(dev)        
        if dev['hostname'] in exemption_device_instance_set:
            exemption_devices.append(dev)
        if dev['hostname'] in offline_devices_instance_set:
            offline_devices.append(dev)

    print(summary)
    
    return summary, no_cover_devices, exemption_devices, offline_devices

def send_stats(summary, no_cover_devices, exemption_devices, offline_devices, args):
    workbook = xlwt.Workbook(encoding='utf-8')
    no_cover_sheet = HIDS_Sheet(workbook, '未覆盖')
    offline_sheet = HIDS_Sheet(workbook, '未运行')
    exemption_sheet = Exemption_Sheet(workbook, '豁免名单')

    for dev in no_cover_devices:
        no_cover_sheet.result_write(dev)
    for dev in offline_devices:
        offline_sheet.result_write(dev)
    for dev in exemption_devices:
        reason = jcssz_db_connection.session.query(HIDSExemptionListNew.Reason).filter_by(DeviceHostName=dev['hostname']).first()
        exemption_sheet.result_write(dev, reason[0])

    workbook.save('/tmp/hids_check.xlsx')

    #print(json.dumps(no_cover_devices, indent=2, ensure_ascii=False))

    from config import mail_user, mail_password, mail_host
    message = []
    message.append(mail_html(summary + devices_to_html_table('本次稽核未覆盖的机器', no_cover_devices).to_html()))
    message.append(mail_file('/tmp/hids_check.xlsx', 'xlsx', 'HIDS稽核'))
    mail_list = gen_mail_list(args)
    send_mail(mail_list, 'HIDS稽核邮件', contents=message, mail_user=mail_user, mail_password=mail_password, mail_host=mail_host)

def _decode_list(data):
    rv = []
    for item in data:
        if isinstance(item, unicode):
            item = item.encode('utf8')
        elif isinstance(item, list):
            item = _decode_list(item)
        rv.append(item)
    return rv

def gen_mail_list(args):
    if not args.mail:
        return ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    else:
        with open(args.mail) as fin:
            mail_list = json.load(fin, object_hook=_decode_list)
    
    return mail_list

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='baseline check script')
    parser.add_argument('-m', '--mail', type=str, default='', help='config path of mail list')
    parser.add_argument('-p', '--path', type=str, default='', help='path of exemption list to be imported')
    parser.add_argument('-u', '--update', action='store_true', default=False, help='update only')
    args = parser.parse_args()
    
    # update device info in exemptionlist db
    update_exemption_db(args)

    if args.update:
        sys.exit(0)

    running_devices = get_cmdb_devices()
    
    hids_online_devices, hids_offline_devices, raw_no_cover_devices = classify_devices(running_devices, get_qingteng_info())

    # statistics
    summary, no_cover_devices, exemption_devices, offline_devices = generate_stats(running_devices, hids_online_devices, hids_offline_devices, raw_no_cover_devices)
    
    # export stats to excel file and send it via email
    send_stats(summary, no_cover_devices, exemption_devices, offline_devices, args)
