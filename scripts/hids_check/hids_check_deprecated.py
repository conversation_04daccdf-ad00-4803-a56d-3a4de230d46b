# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys, os
reload(sys)
sys.setdefaultencoding('utf-8')

# module search paths
public_resources = '/home/<USER>/resources'
hids_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.dirname(os.path.dirname(hids_path)) #../
utils_path = root_path + '/utils' #../utils

sys.path.append(public_resources)
sys.path.append(root_path)
sys.path.append(utils_path)

import hashlib
import json
import time

import requests
import sqlalchemy

from db_connection import SqliteMemoryConnection, MysqlConnection
from item import Device_MEM, HIDSBlackList
from utils.my_mail import *
from utils.export_xlsx import XlsExporter
from utils import devices_to_html_table


class QingTengHelper:
    def __init__(self, address='qingtengurl', user='qingtenguser', password='qingtengpassword'):
        self.address = address
        self.user = user
        self.password = password
        self._auth()

    def _auth(self):
        header = {"Content-Type": "application/json"}
        data = {"username": self.user, "password": self.password}
        re = requests.post(url=self.address + '/v1/api/auth', data=json.dumps(data), headers=header)
        if not re.json():
            raise Exception('Unauthorized!')
        self.signKey = re.json().get('data').get('signKey')
        self.jwt = re.json().get('data').get('jwt')
        self.comId = re.json().get('data').get('comId')

    def request(self, api, payload, method='get'):
        ts = int(time.time())
        if not payload:
            to_sign = self.comId + str(ts) + self.signKey
        else:
            info = ""
            if method == 'get':
                keys = sorted(payload.keys())
                for key in keys:
                    info = info + key + str(payload.get(key))

            elif method in ('post', 'put', 'delete'):
                info = json.dumps(payload)
            to_sign = self.comId + info + str(ts) + self.signKey
        print(to_sign)
        sign = hashlib.sha1(to_sign).hexdigest()
        header = {
            "Content-Type": "application/json",
            "comId": self.comId,
            "timestamp": str(ts),
            "sign": sign,
            "Authorization": "Bearer " + self.jwt
        }
        re = requests.request(method=method, url=self.address + api, data=json.dumps(payload), headers=header)
        return re

    def get_linux_devices(self):
        all_rows = []
        init_re = self.request(api='/external/api/assets/host/linux', payload=None, method='get')
        total_size = init_re.json()['total']
        all_rows.extend(init_re.json()['rows'])
        page = 1
        for batch in range(50, total_size, 50):
            cur_re = self.request(api='/external/api/assets/host/linux?page={}&size=50'.format(page),
                                  payload={'page': page, 'size': 50}, method='get')
            all_rows.extend(cur_re.json()['rows'])
            page += 1
        return all_rows

    def get_win_devices(self):
        all_rows = []
        init_re = self.request(api='/external/api/assets/host/win', payload=None, method='get')
        total_size = init_re.json()['total']
        all_rows.extend(init_re.json()['rows'])
        page = 1
        for batch in range(50, total_size, 50):
            cur_re = self.request(api='/external/api/assets/host/win?page={}&size=50'.format(page),
                                  payload={'page': page, 'size': 50}, method='get')
            all_rows.extend(cur_re.json()['rows'])
            page += 1
        return all_rows


def init_black_list():
    myc = MysqlConnection()
    jcssz_faild_devices = sqlite_mem_con.session.query(Device_MEM).filter(
        Device_MEM.Type == u'服务器',
        Device_MEM.CIStatus == u'运行中',
        Device_MEM.ManagerGroup == u'服务器及存储基础设施组',
        sqlalchemy.and_(
            Device_MEM.CSOSVersion.notlike('%CentOS%'),
            Device_MEM.CSOSVersion.notlike('%Red Hat%'),
            Device_MEM.CSOSVersion.notlike('%Kylin%'),
            Device_MEM.CSOSVersion.notlike('%Ubuntu%'),
            Device_MEM.CSOSVersion.notlike('%Euler%'),
            Device_MEM.CSOSVersion.notlike('%win%'),
        )
    )

    for device in jcssz_faild_devices:
        cur_d = HIDSBlackList()
        for k, v in device.__dict__.items():
            if k != '_sa_instance_state' and 'Device' + k in HIDSBlackList.__dict__.keys():
                cur_d.__dict__['Device' + k] = v
        myc.session.merge(cur_d)
    myc.session.commit()


sqlite_mem_con = SqliteMemoryConnection()


def add_black_list(device_names):
    myc = MysqlConnection()
    for name in device_names:
        devices = sqlite_mem_con.get_device(name=name)
        if not devices:
            print('{} not found'.format(name))
            continue
        cur_d = HIDSBlackList()
        device = devices[0]
        for k, v in device.__dict__.items():
            if k != '_sa_instance_state' and 'Device' + k in HIDSBlackList.__dict__.keys():
                cur_d.__dict__['Device' + k] = v
            if cur_d.DeviceVIPAdress:
                cur_d.DeviceVIPAdress = cur_d.DeviceVIPAdress[:50]
        myc.session.merge(cur_d)
    myc.session.commit()


def update_black_list():
    myc = MysqlConnection()
    all_black = myc.session.query(HIDSBlackList).all()
    all_names = [a.DeviceHostName for a in all_black]
    add_black_list(all_names)


if __name__ == '__main__':
    update_black_list()
    qth = QingTengHelper()
    all_linux = qth.get_linux_devices()
    all_win = qth.get_win_devices()

    # 一般这种情况是主机有些变化，例如cpu\内存、磁盘之类的变动，导致id变了，原agent离线新agent重新上线
    # 所以对于在线，离线，需要去重

    online_hostname_list = [a.get('hostname').split('.novalocal')[0] for a in all_linux + all_win if
                            a['onlineStatus'] == 1]
    offline_hostname_list = [a.get('hostname').split('.novalocal')[0] for a in all_linux + all_win if
                             a['onlineStatus'] == 0]
    online_display_ip = [a.get('displayIp') for a in all_linux + all_win if a['onlineStatus'] == 1]
    offline_display_ip = [a.get('displayIp') for a in all_linux + all_win if a['onlineStatus'] == 0]
    true_online_hostname_set = set(online_hostname_list)
    true_offline_hostname_set = set(offline_hostname_list) - set(online_hostname_list)
    true_online_display_ip_set = set(online_display_ip)
    true_offline_display_ip_set = set(offline_display_ip) - set(online_display_ip)

    jcssz_devices = sqlite_mem_con.session.query(Device_MEM).filter(
        Device_MEM.CIStatus == u'运行中',
        Device_MEM.Type == u'服务器',
        Device_MEM.ManagerGroup == u'服务器及存储基础设施组',
        Device_MEM.CIStatus != u'下线'
    ).all()

    hids_online_devices = set()
    hids_offline_devices = set()
    no_cover_devices = set()

    for device in jcssz_devices:
        hostname_online_flag = False
        hostname_offline_flag = False
        ip_online_flag = False
        ip_offline_flag = False
        if device.HostName in true_online_hostname_set:
            hostname_online_flag = True
        if device.HostName in true_offline_hostname_set:
            hostname_offline_flag = True
        if not device.IPAddress:
            continue
        for ip in device.IPAddress.split(','):
            if ip in true_online_display_ip_set:
                ip_online_flag = True
            if ip in true_offline_display_ip_set:
                ip_offline_flag = True
        # 两个如果在一个，那就当在线
        if hostname_online_flag or ip_online_flag:
            hids_online_devices.add(device)
        # 同理，如果在离线
        elif hostname_offline_flag or ip_offline_flag:
            hids_offline_devices.add(device)
        # 如果都不在，那就没覆盖到
        elif not hostname_online_flag and not ip_online_flag:
            no_cover_devices.add(device)

    myc = MysqlConnection()
    # myc.session.delete(HIDSBlackList).filter(HIDSBlackList.DeviceCIStatus=='下线'.decode('utf8'))
    black_device_instance_set = set([a[0] for a in myc.session.query(HIDSBlackList.DeviceHostName).filter(
        HIDSBlackList.DeviceCIStatus == u'运行中',
        HIDSBlackList.DeviceType == u'服务器',
        HIDSBlackList.DeviceManagerGroup == u'服务器及存储基础设施组'
    ).all()])

    # 整理，通过DeviceInstanceID唯一
    # 230801 设置为通过hostname唯一
    jcssz_devices_instance_set = set([d.HostName for d in jcssz_devices])

    online_devices_instance_set = set([d.HostName for d in hids_online_devices])
    offline_devices_instance_set = set([d.HostName for d in hids_offline_devices])
    no_cover_devices_instance_set = set([d.HostName for d in no_cover_devices]) - set(black_device_instance_set)
    error_devices_instance_set = online_devices_instance_set & black_device_instance_set

    # 在黑名单但是安装的机器
    black_device_instance_set - (
            jcssz_devices_instance_set - (offline_devices_instance_set | online_devices_instance_set))

    ans = "管理主机数量 {}；豁免主机数量 {}；应安装主机数量 {}；" \
          "已安装数量 {}；HIDS覆盖率 {}%；" \
          "已运行HIDS数量 {}；未运行数量 {}；在线率 {}%".format(
        len(jcssz_devices_instance_set), len(black_device_instance_set),
        len(jcssz_devices_instance_set) - len(black_device_instance_set),
        len(online_devices_instance_set | offline_devices_instance_set),
        round((len(online_devices_instance_set | offline_devices_instance_set)) * 1.000000 / (
                len(jcssz_devices_instance_set) - len(black_device_instance_set)) * 100, 2),
        len(online_devices_instance_set), len(offline_devices_instance_set),
        round(len(online_devices_instance_set) * 1.000000 / (
                len(online_devices_instance_set) + len(offline_devices_instance_set)) * 100
              , 2))
    print(ans)

    no_cover_devices = sqlite_mem_con.session.query(Device_MEM).filter(
        Device_MEM.HostName.in_(list(no_cover_devices_instance_set)),
        Device_MEM.CIStatus != u'下线'
    )
    offline_devices = sqlite_mem_con.session.query(Device_MEM).filter(
        Device_MEM.HostName.in_(list(offline_devices_instance_set)),
        Device_MEM.CIStatus != u'下线'
    )
    black_devices = sqlite_mem_con.session.query(Device_MEM).filter(
        Device_MEM.HostName.in_(list(black_device_instance_set)),
        Device_MEM.CIStatus != u'下线'
    )

    xlsx_path = '/tmp/hids_check.xlsx'
    xe = XlsExporter(xlsx_path)
    xe.write_devices('未覆盖', no_cover_devices)
    xe.write_devices('未运行', offline_devices)
    xe.write_devices('黑名单', black_devices)
    xe.save_workbook()

    #print(no_cover_devices)

    from resources.config import mail_user, mail_password, mail_host
    message = []
    message.append(mail_html(ans + devices_to_html_table('本次稽核未覆盖的机器', no_cover_devices).to_html()))
    message.append(mail_file(xlsx_path, 'xlsx', 'HIDS稽核'))
    send_mail(['<EMAIL>', '<EMAIL>', '<EMAIL>'], 'HIDS稽核邮件', contents=message, mail_user=mail_user, mail_password=mail_password, mail_host=mail_host)
