# -*- coding: utf-8 -*-
# !/usr/bin/python

"""
测试修复后的 Ansible 脚本
"""

import sys
import os

# 添加父目录到路径
sys.path.append('../')

def test_simple_task():
    """
    测试简单的 Ansible 任务
    """
    try:
        from my_ansible import MyAnsibleService, DEFAULT_PING_PLAY_SOURCE
        
        print("测试简单的 ping 任务...")
        
        # 创建简单的 play
        simple_play = {
            "name": "Simple test play",
            "hosts": "localhost",
            "gather_facts": False,
            "tasks": [
                {
                    "name": "Test debug message",
                    "action": "debug",
                    "args": {
                        "msg": "Hello from Ansible!"
                    }
                }
            ]
        }
        
        # 初始化 Ansible 服务
        mas = MyAnsibleService(
            connection='local',
            inventory='localhost,',
            forks=1,
            verbosity=1
        )
        
        # 运行任务
        result = mas.run(simple_play)
        
        print(f"任务执行结果: {result}")
        print("回调信息:")
        
        for info in mas.results_callback.all_info:
            print(f"  - {info['task_name']}: {info['status']}")
            
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_callback_module():
    """
    测试回调模块
    """
    try:
        from my_ansible import MyCallbackModule
        
        print("测试回调模块初始化...")
        
        callback = MyCallbackModule()
        
        # 检查必要的属性
        assert hasattr(callback, '_display'), "缺少 _display 属性"
        assert hasattr(callback, 'host'), "缺少 host 属性"
        assert hasattr(callback, 'all_info'), "缺少 all_info 属性"
        
        print("回调模块测试通过!")
        return True
        
    except Exception as e:
        print(f"回调模块测试失败: {e}")
        return False


if __name__ == '__main__':
    print("开始测试修复后的 Ansible 代码...")
    print("=" * 50)
    
    # 测试回调模块
    callback_test = test_callback_module()
    
    # 测试简单任务
    simple_test = test_simple_task()
    
    print("=" * 50)
    print("测试结果:")
    print(f"  回调模块测试: {'通过' if callback_test else '失败'}")
    print(f"  简单任务测试: {'通过' if simple_test else '失败'}")
    
    if callback_test and simple_test:
        print("所有测试通过! 修复成功。")
    else:
        print("部分测试失败，需要进一步调试。")
