# -*- coding: utf-8 -*-
# !/usr/bin/python

import json
import os
import socket
# import urllib
from datetime import datetime

import requests
import sqlalchemy
# try:
#     from urllib import quote
# except ImportError:
#     from urllib.parse import quote
from six.moves.urllib.parse import quote
from sqlalchemy.orm import sessionmaker

import item
from item import *



class MysqlConnection:
    from config import mysql_user, mysql_password, mysql_address, table as mysql_table

    def __init__(self, user = mysql_user, password = mysql_password, address = mysql_address, table = mysql_table, sqlalchemy_base=MYSQL_4226):
        self.user = user
        self.password = password
        self.address = address
        self.sqlalchemy_base = sqlalchemy_base
        self._engine = sqlalchemy.create_engine('mysql+pymysql://' + self.user + ':' + quote(
            self.password) + '@' + self.address + '/{}?charset=utf8'.format(table))
        DBsession = sessionmaker(bind=self._engine)
        self.session = DBsession()

    def create_table(self):
        self.sqlalchemy_base.metadata.create_all(self._engine)



class SqliteMemoryConnection:
    def __init__(self):
        self._engine = sqlalchemy.create_engine('sqlite:///:memory:')
        DBsession = sessionmaker(bind=self._engine)
        self.session = DBsession()
        self.create_table()
        self.init_db()

    def create_table(self):
        SQLITE_MEM.metadata.create_all(self._engine)

    @staticmethod
    def cmdb_object_to_mem(cmdb_object):
        """
        凑合用吧
        :param cmdb_object:
        :return:
        """
        mem_object = None
        if type(cmdb_object) == item.Device_CMDB:
            mem_object = item.Device_MEM()
        elif type(cmdb_object) == item.App_CMDB:
            mem_object = item.App_MEM()
        elif type(cmdb_object) == item.AppAndDevice_CMDB:
            mem_object = item.AppAndDevice_MEM(device_id=None, device_ciname=None)
        for k, v in cmdb_object.__dict__.items():
            if k != '_sa_instance_state':
                mem_object.__dict__[k] = v
        if type(mem_object) in (item.Device_MEM, item.App_MEM):
            # todo timestamp
            for var in dir(mem_object):
                if 'Date' in var and mem_object.__dict__[var]:
                    mem_object.__dict__[var] = datetime.fromtimestamp(mem_object.__dict__[var])
        elif type(mem_object) == item.AppAndDevice_MEM:
            mem_object.DeviceModifiedDate = datetime.fromtimestamp(mem_object.DeviceModifiedDate)
        return mem_object

    def init_db(self, class_types=(item.Device_CMDB,)):
        print('Init_MEM_DEVICE...')
        # todo
        from resources.config import cmdb_reader_address, cmdb_reader_user, cmdb_reader_password
        cmdb_r = CMDBConnection(
            user=cmdb_reader_user,
            address=cmdb_reader_address,
            password=cmdb_reader_password,
        )
        for c in class_types:
            all_cmdb_objects = cmdb_r.session.query(c).all()
            for e in all_cmdb_objects:
                self.session.add(SqliteMemoryConnection.cmdb_object_to_mem(e))
        self.session.commit()

    def init_app_and_device_by_sql(self):
        print('Init_MEM_APP_AND_DEVICE...')
        cmdb_r = CMDBConnection()
        all_cmdb_app_and_device = cmdb_r.session.query(AppAndDevice_CMDB).all()
        mem_objects = []
        for i, cmdb_device in enumerate(all_cmdb_app_and_device):
            if not cmdb_device:
                continue
            cur_mem_device = AppAndDevice_MEM(
                device_id=cmdb_device.InstanceID,
                device_ciname=cmdb_device.CINAME
            )
            for k, v in cmdb_device.__dict__.items():
                if k != '_sa_instance_state':
                    cur_mem_device.__dict__[k] = v
            if cur_mem_device.ModifiedDate:
                cur_mem_device.ModifiedDate = datetime.fromtimestamp(cur_mem_device.ModifiedDate)
            mem_objects.append(cur_mem_device)
        self.session.add_all(mem_objects)
        self.session.commit()

    def init_devices_by_API(self):
        print('Init_SQLITE_devices_BY_API')
        mem_devices = []
        # todo
        SQL = "select DISTINCT * FROM ARSystem.dbo.SZSE_CMDB_AppAndDevice WHERE DeviceInstanceID is not null"
        response = requests.post(
            # url='http://cmdb.saas.cn/selectcmdb',
            url='http://10.132.5.13:1433/selectcmdb',
            headers={"Content-Type": "application/json"},
            data=json.dumps({
                # "userkey": "os monitor",
                "userkey": "test",
                "sql": SQL
            }))
        if not response.ok:
            raise Exception('Query CMDB Failed!')
        ans = response.json()
        if ans.get('returnCode') != 0:
            raise Exception('Query CMDB Failed!')
        for i, cmdb_device_d in enumerate(ans.get('data')):
            cur_mem_device = AppAndDevice_MEM(
                device_id=cmdb_device_d.get('DeviceInstanceID'),
                device_ciname=cmdb_device_d.get('DeviceCIName')
            )
            for k, v in cmdb_device_d.items():
                if k != '_sa_instance_state':
                    cur_mem_device.__dict__[k] = v
            if cur_mem_device.ModifiedDate:
                cur_mem_device.ModifiedDate = datetime.fromtimestamp(cur_mem_device.ModifiedDate)
            mem_devices.append(cur_mem_device)
        self.session.add_all(mem_devices)
        self.session.commit()

    def get_device(self, ip=None, name=None, running=True):
        # todo ip,name可能重复
        if ip:
            devices = self.session.query(Device_MEM).filter(
                sqlalchemy.or_(
                    Device_MEM.IPAddress.like('%{}%'.format(ip)),
                    Device_MEM.ManagementAddress.like('%{}%'.format(ip)),
                    Device_MEM.VIPAdress.like('%{}%'.format(ip)),
                    Device_MEM.IPAddressAll.like('%{}%'.format(ip)),
                ),
                Device_MEM.Type == u'服务器',
                Device_MEM.Type != u'存储设备',
            ).all()
            if not devices:
                print('{} not found in cmdb'.format(ip))
                return None
            else:
                return devices
        if name:
            devices = self.session.query(Device_MEM).filter(
                sqlalchemy.or_(
                    sqlalchemy.sql.func.lower(Device_MEM.CINAME) == name.lower(),
                    sqlalchemy.sql.func.lower(Device_MEM.HostName) == name.lower(),
                    sqlalchemy.sql.func.lower(Device_MEM.ShortDescription) == name.lower(),
                    sqlalchemy.sql.func.lower(Device_MEM.Description) == name.lower(),
                ),
                Device_MEM.Type == u'服务器',
                # Device_MEM.DeviceCIStatus == u'运行中'
            ).all()
            if not devices:
                all_device = self.session.query(Device_MEM).filter(
                    # Device_MEM.DeviceCIStatus == u'运行中'
                ).all()
                for device in all_device:
                    if not device.CINAME.lower:
                        continue
                    if not device.HostName:
                        continue
                    if name.lower() in [device.CINAME.lower(), device.HostName.lower()]:
                        return [device, ]
                print('{} not found in cmdb'.format(name))
                return None
            else:
                if len(devices) >= 2:
                    res = devices[0]
                    for device in devices[1:]:
                        if res.ModifiedDate < device.ModifiedDate:
                            res = device
                    return [res, ]
                else:
                    return devices


class SqliteLocalConnection:
    def __init__(self):
        self.device_name = socket.gethostname()
        self.database_file = self.device_name + '.db'
        self._engine = sqlalchemy.create_engine('sqlite:///' + self.database_file)
        DBsession = sessionmaker(bind=self._engine)
        self.session = DBsession()

    def create_database(self):
        if not os.path.exists(self.database_file):
            SQLITE_LOCAL.metadata.create_all(self._engine)


class CMDBConnection:
    def __init__(self, address, password, user):
        self._engine = sqlalchemy.create_engine(
            'mssql+pymssql://' + user + ':' + password + '@' + address + '/ARSystem')
        DBsession = sessionmaker(bind=self._engine)
        self.session = DBsession()
