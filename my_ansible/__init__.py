# -*- coding: utf-8 -*-
# !/usr/bin/python
import datetime
import json
import os.path
import shutil
import time

from ansible import constants as C
from ansible import context
from ansible.constants import DEFAULT_VAULT_ID_MATCH
from ansible.executor.playbook_executor import PlaybookExecutor
from ansible.executor.task_queue_manager import TaskQueueManager
from ansible.inventory.host import Group  # 对 主机组 执行操作 ，可以给组添加变量等操作，扩展
from ansible.inventory.manager import InventoryManager
from ansible.module_utils.common.collections import ImmutableDict
from ansible.parsing.dataloader import DataLoader
from ansible.parsing.vault import VaultAES256
from ansible.parsing.vault import VaultLib, VaultSecret
from ansible.playbook.play import Play
from ansible.plugins.callback.default import CallbackModule as DefaultCallbackModule
from ansible.vars.manager import VariableManager

C.HOST_KEY_CHECKING = False


# vault格式如下
# {"*************": {
#     "connections": [
#         {
#             "password": "password",
#             "user": "user1"
#         },
# },....}


PING_VAULT_PLAYBOOK_TEMPLATE = """---
- name: Example Playbook using Vault data with fallback logic
  hosts: all
  gather_facts: no
  vars:
    - default_user: "{}"
  vars_files:
    - {}
  tasks:
    - name: Find and set connection
      set_fact:
        select_connection:  >
          {{
            (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list | first)
            if (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list) | length > 0
            else vars[inventory_hostname].connections[0]
          }}
    - name: set fact from connection
      set_fact:
        ansible_user: "{{ select_connection.user }}"
        ansible_ssh_pass: "{{ select_connection.password }}"
    - name: ping task
      ping:
"""

TEST_PING_VAULT_PLAYBOOK_TEMPLATE = """---
- name: Ping
  hosts: all
  gather_facts: no
  tasks:
    - name: ping task
      ping:
"""

DEFAULT_PING_PLAY_SOURCE = {
    'name': "Default Ping",
    'hosts': 'all',
    'gather_facts': 'no',
    'tasks': [
        # 这里每个 task 就是这个列表中的一个元素，格式是嵌套的字典
        # 也可以作为参数传递过来，这里就简单化了。
        # {"module": 'ping', "args": '', "async": 0, "poll": 0},
        {'ping': {}, },
    ]
}
DEFAULT_VAULT_PLAY_SOURCE = \
    {
        'name': 'default_playbook_use_vualt',
        'hosts': 'all',
        'gather_facts': 'no',
        'tasks': [
            {
                'name': 'get connection',
                'set_fact': {
                    'select_connection': """{{ (vars[inventory_hostname].connections  | selectattr('user', 'equalto', default_user) | list | first) if (vars[inventory_hostname].connections | selectattr('user', 'equalto', default_user) | list) | length > 0 else vars[inventory_hostname].connections[0] }}""",
                }
            },
            {
                'name': 'get password',
                'set_fact': {
                    'ansible_user': "{{ select_connection.user }}",
                    'ansible_ssh_pass': "{{ select_connection.password }}"
                }
            },
        ]
    }
DEFAULT_VAULT_EXTRA_VARS = {'default_user': 'roota'}


def add_info(func):
    """
    装饰器：为每个IP维护一个完整的任务执行历史列表
    数据结构：
    - self.all_info: 所有任务的完整列表（保持原有格式）
    - self.host: 按状态分类的主机结果（保持原有格式）
    - self.host_results: 每个IP的完整任务执行历史
    """
    def wrapper(self, result, *args, **kwargs):
        method_name = func.__name__
        runner_status = method_name.split('_')[-1]
        host_ip = result._host.get_name()

        # 构建详细的结果信息
        result_dict = {
            "exec_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "host_ip": host_ip,
            "task_name": result.task_name,
            "status": runner_status,
            "result_info": result._result,
            "play_name": getattr(result._task, '_play', {}).get('name', 'Unknown Play'),
            "task_uuid": getattr(result._task, '_uuid', None),
            "duration": getattr(result, '_duration', None)
        }

        # 保持原有的 all_info 列表（向后兼容）
        self.all_info.append(result_dict)

        # 保持原有的 host 字典结构（向后兼容）
        if runner_status not in self.host:
            self.host[runner_status] = {}
        self.host[runner_status][host_ip] = result._result

        # 新增：为每个IP维护完整的任务执行历史
        if not hasattr(self, 'host_results'):
            self.host_results = {}

        if host_ip not in self.host_results:
            self.host_results[host_ip] = []

        self.host_results[host_ip].append(result_dict)

        return func(self, result, *args, **kwargs)

    return wrapper


class MyCallbackModule(DefaultCallbackModule):
    '''
    This is the default callback interface, which simply prints messages
    to stdout when new callback events are received.
    '''

    CALLBACK_NAME = 'my_default_callback'

    def __init__(self):
        super(MyCallbackModule, self).__init__()

        # 确保 _display 属性存在
        if not hasattr(self, '_display'):
            from ansible.utils.display import Display
            self._display = Display()

        self.display_skipped_hosts = True
        self.display_ok_hosts = True
        self.show_per_host_start = True

        # 原有数据结构（向后兼容）
        self.host = {}
        self.all_info = []

        # 新增：每个IP的完整任务执行历史
        self.host_results = {}

        self.task_count = 0
        self.current_task = 0

    @add_info
    def v2_runner_on_failed(self, result, ignore_errors=False):
        """
        优化的失败处理方法，避免回调插件警告
        """
        try:
            # 确保有 _display 属性
            if not hasattr(self, '_display'):
                from ansible.utils.display import Display
                self._display = Display()

            # 添加缺失的属性以避免报错
            if not hasattr(self, 'display_failed_stderr'):
                self.display_failed_stderr = True

            # 调用父类方法以保持兼容性
            super(MyCallbackModule, self).v2_runner_on_failed(result, ignore_errors)

        except AttributeError as e:
            # 处理属性错误，避免回调插件警告
            if 'show_task_path_on_failure' in str(e):
                # 忽略 show_task_path_on_failure 相关的警告
                pass
            else:
                print(f"Warning: Callback plugin error: {str(e)}")
        except Exception as e:
            print(f"Error in v2_runner_on_failed: {str(e)}")

    @add_info
    def v2_runner_on_ok(self, result):
        super(MyCallbackModule, self).v2_runner_on_ok(result)

    @add_info
    def v2_runner_on_skipped(self, result):
        super(MyCallbackModule, self).v2_runner_on_skipped(result)

    @add_info
    def v2_runner_on_unreachable(self, result):
        super(MyCallbackModule, self).v2_runner_on_unreachable(result)

    def v2_runner_on_start(self, host, task):
        super(MyCallbackModule, self).v2_runner_on_start(host, task)
        self.task_count += 1
        self.current_task += 1

    # def get_unreachable_task(self):
    #     pass

    def get_all_failed_ip(self):
        return sorted(list(set(self.host.get('failed', {}).keys()) | set(self.host.get('unreachable', {}).keys())))

    def get_unreachable_ip(self):
        return sorted([host_ip for host_ip, tasks in self.host_results.items()
                      if any(task['status'] == 'unreachable' for task in tasks)])

    def get_failed_ip(self):
        return sorted([host_ip for host_ip, tasks in self.host_results.items()
                      if any(task['status'] == 'failed' for task in tasks)])

    def get_host_results(self, host_ip=None):
        """
        获取指定主机或所有主机的完整任务执行历史

        Args:
            host_ip: 主机IP，如果为None则返回所有主机的结果

        Returns:
            dict 或 list: 主机的任务执行历史
        """
        if host_ip:
            return self.host_results.get(host_ip, [])
        return self.host_results

    def get_host_summary(self, host_ip):
        """
        获取指定主机的执行摘要

        Args:
            host_ip: 主机IP

        Returns:
            dict: 包含各种状态统计的摘要
        """
        host_tasks = self.host_results.get(host_ip, [])
        summary = {
            'total_tasks': len(host_tasks),
            'ok': 0,
            'failed': 0,
            'skipped': 0,
            'unreachable': 0,
            'tasks': host_tasks
        }

        for task in host_tasks:
            status = task.get('status', 'unknown')
            if status in summary:
                summary[status] += 1

        return summary

    def get_all_hosts_summary(self):
        """
        获取所有主机的执行摘要

        Returns:
            dict: 每个主机的摘要信息
        """
        return {host_ip: self.get_host_summary(host_ip)
                for host_ip in self.host_results.keys()}

    def get_tasks_by_status(self, host_ip, status):
        """
        获取指定主机指定状态的任务

        Args:
            host_ip: 主机IP
            status: 任务状态 ('ok', 'failed', 'skipped', 'unreachable')

        Returns:
            list: 符合条件的任务列表
        """
        host_tasks = self.host_results.get(host_ip, [])
        return [task for task in host_tasks if task.get('status') == status]


class MyAnsibleVaultManager(object):
    """
    use json encrypt vault
    """

    def __init__(self, vault_file, vault_password):
        self.vault_file_path = vault_file
        self.__vault_secret = VaultSecret(vault_password.encode())
        self.vault = VaultLib([(DEFAULT_VAULT_ID_MATCH, self.__vault_secret)])
        self.vault_vars = None

    def encrypt_file(self):
        try:
            vault_aes = VaultAES256()
            encrypted_data = vault_aes.encrypt(
                json.dumps(self.vault_vars, indent=2, ensure_ascii=False).encode(),
                self.__vault_secret
            )
            if os.path.exists(self.vault_file_path):
                os.system("cp {} {}".format(
                    self.vault_file_path,
                    self.vault_file_path + '.bak' + time.strftime(
                        "%Y-%m-%d",
                        time.localtime(time.time())
                    )
                )
                )
            with open(self.vault_file_path, 'wb') as fo:
                fo.write(b'$ANSIBLE_VAULT;1.1;AES256\n')
                fo.write(encrypted_data)
        except Exception as e:
            print(e.message)

    def decrypt_file(self):
        try:
            with open(self.vault_file_path) as file:
                encrypted_content = file.read()
                res = json.loads(self.vault.decrypt(encrypted_content))
                self.vault_vars = res
        except Exception as e:
            print(e)

    def probe_vault_passwords(self, timeout=5, max_workers=10, save_changes=True):
        """
        探测vault中保存的密码是否仍然有效
        如果不可达就删除对应的connections

        Args:
            timeout: SSH连接超时时间（秒）
            max_workers: 最大并发连接数
            save_changes: 是否自动保存更改到vault文件

        Returns:
            dict: 探测结果报告
        """
        if not self.vault_vars:
            print("Vault数据为空，请先解密vault文件")
            return {}

        print("开始探测vault中的密码有效性...")

        # 收集所有需要测试的连接
        test_connections = []
        for host_ip, host_data in self.vault_vars.items():
            if isinstance(host_data, dict) and 'connections' in host_data:
                for connection in host_data['connections']:
                    if 'user' in connection and 'password' in connection:
                        test_connections.append({
                            'host_ip': host_ip,
                            'user': connection['user'],
                            'password': connection['password'],
                            'connection_data': connection
                        })

        if not test_connections:
            print("未找到需要测试的连接信息")
            return {}

        print(f"找到 {len(test_connections)} 个连接需要测试")

        # 并发测试连接
        from concurrent.futures import ThreadPoolExecutor, as_completed

        results = {
            'tested': 0,
            'reachable': 0,
            'unreachable': 0,
            'removed_connections': [],
            'valid_connections': [],
            'errors': []
        }

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有测试任务
            future_to_connection = {
                executor.submit(self._test_single_connection, conn, timeout): conn
                for conn in test_connections
            }

            # 处理完成的任务
            for future in as_completed(future_to_connection):
                connection = future_to_connection[future]
                results['tested'] += 1

                try:
                    is_reachable = future.result()

                    if is_reachable:
                        results['reachable'] += 1
                        results['valid_connections'].append({
                            'host_ip': connection['host_ip'],
                            'user': connection['user']
                        })
                        print(f"✓ {connection['host_ip']} ({connection['user']}) - 可达")
                    else:
                        results['unreachable'] += 1
                        results['removed_connections'].append({
                            'host_ip': connection['host_ip'],
                            'user': connection['user']
                        })
                        print(f"✗ {connection['host_ip']} ({connection['user']}) - 不可达，将删除")

                        # 从vault中删除无效连接
                        self._remove_invalid_connection(
                            connection['host_ip'],
                            connection['connection_data']
                        )

                except Exception as e:
                    results['errors'].append({
                        'host_ip': connection['host_ip'],
                        'user': connection['user'],
                        'error': str(e)
                    })
                    print(f"⚠ {connection['host_ip']} ({connection['user']}) - 测试出错: {e}")

        # 清理空的主机条目
        self._cleanup_empty_hosts()

        # 保存更改
        if save_changes and (results['removed_connections'] or results['errors']):
            try:
                self.encrypt_file()
                print("✓ 已保存vault文件更改")
            except Exception as e:
                print(f"✗ 保存vault文件失败: {e}")
                results['errors'].append({'save_error': str(e)})

        # 打印摘要
        self._print_probe_summary(results)

        return results


class MyAnsibleService:
    # 自定义类的一些初始化信息,在下面的context.CLIARGS初始化函数中使用这些初始化属性
    # 在初始化的这个类时候可以传参，以便覆盖默认选项的值,我们可以自己传参数，否则使用定义的默认值
    def __init__(
            self,
            connection='ssh',  # 连接方式 local 本地方式，smart ssh方式
            remote_user="None",  # 远程用户
            ack_pass=None,  # 提示输入密码
            sudo=None, sudo_user=None, ask_sudo_pass=None,
            module_path=None,  # 模块路径，可以指定一个自定义模块的路径
            become=None,  # 是否提权
            become_method=None,  # 提权方式 默认 sudo 可以是 su
            become_user=None,  # 提权后，要成为的用户，并非登录用户
            become_password=None,
            check=False, diff=False,
            listhosts=False, listtasks=False, listtags=False,
            forks=5,  # 同时执行的主机数量
            tags=[],  # 执行的tags列表
            skip_tags=[],  # skip跳过的tags列表
            verbosity=3,
            syntax=None,
            start_at_task=None,
            inventory=None,
            ssh_password=None,  # ssh 用户的密码，应该是一个字典, key 必须是 conn_pass
            vault_pass=None,
            password_vault_path=None,
            **kwargs
    ):
        """
         remote_user="None",  # 远程用户
         password=None,  # ssh 用户的密码，应该是一个字典, key 必须是 conn_pass
         ack_pass=None,  # 提示输入密码
         sudo=None, sudo_user=None, ask_sudo_pass=None,
         module_path=None,  # 模块路径，可以指定一个自定义模块的路径
         become=None,  # 是否提权
         become_method=None,  # 提权方式 默认 sudo 可以是 su
         become_user=None,  # 提权后，要成为的用户，并非登录用户
         check=False, diff=False,
         listhosts=None, listtasks=None, listtags=None,
         forks=5,  # 同时执行的主机数量
         tags=[],  # 执行的tags列表
         skip_tags=[],  # skip跳过的tags列表

         verbosity=3,
         start_at_task=None,
        """
        if vault_pass and password_vault_path:
            self.vault_manager = MyAnsibleVaultManager(
                vault_file=password_vault_path,
                vault_password=vault_pass
            )

        context.CLIARGS = ImmutableDict(
            connection=connection,
            remote_user=remote_user,
            ack_pass=ack_pass,
            sudo=sudo,
            sudo_user=sudo_user,
            ask_sudo_pass=ask_sudo_pass,
            module_path=module_path,
            become=become,
            become_method=become_method,
            become_user=become_user,
            verbosity=verbosity,
            listhosts=listhosts,
            listtasks=listtasks,
            listtags=listtags,
            forks=forks,
            tags=tags,
            skip_tags=skip_tags,
            syntax=syntax,
            start_at_task=start_at_task,
            check=check,
            diff=diff,
            **kwargs
        )

        # 三元表达式，假如没有传递 inventory文件, 就使用 "localhost,"
        # 这里需要注意如果不使用host文件，即inventory未传入，直接动态传入host列表的情况，
        # 这里会将localhost加入主机中，所以执行all主机组也会执行本机，所以要么改为未传入就为“”，要么就设置其他的主机组名称，不使用all执行全部
        # inventory 就是平时用到的存放主机ip以及变量的资源库文件，-i 参数后面跟的文件
        self.inventory = inventory if inventory else "localhost,"

        # 实例化数据解析器,用于解析 存放主机列表的资源库文件 （比如： /etc/ansible/hosts） 中的数据和变量数据的
        self.loader = DataLoader()

        # 实例化 资产配置对象,InventoryManager管理资源库，可以指定一个loader数据解析器和一个inventory文件
        self.inv_obj = InventoryManager(loader=self.loader, sources=self.inventory)

        # 设置密码，可以为空字典，但必须有此参数
        self.passwords = {
            "conn_pass": ssh_password,
            "become_pass": become_password
        }

        # 实例化回调插件对象
        # self.display = Display()
        # self.display.verbosity = verbosity
        self.results_callback = MyCallbackModule()
        # self.results_callback._display = self.display
        # self.results_callback = DefaultCallbackModule()

        # 变量管理器,假如有变量，所有的变量应该交给他管理。 这里他会从 inventory 对象中获取到所有已定义好的变量。 这里也需要数据解析器。
        self.variable_manager = VariableManager(self.loader, self.inv_obj)

        self.tqm = None

    def load_vault_file(self):
        self.vault_manager.decrypt_file()
        self.variable_manager.extra_vars.update(self.vault_manager.vault_vars)

    def run(self, play_source=None, extra_vars=None):
        """
        参数说明：
        task_time -- 执行异步任务时等待的秒数，这个需要大于 0 ，等于 0 的时候不支持异步（默认值）。这个值应该等于执行任务实际耗时时间为好
        """

        if extra_vars is None:
            extra_vars = {}
        self.variable_manager.extra_vars.update(extra_vars)
        if play_source is None:
            play_source = DEFAULT_PING_PLAY_SOURCE

        play = Play().load(
            play_source,
            variable_manager=self.variable_manager,
            loader=self.loader
        )
        result = None
        try:
            self.tqm = TaskQueueManager(
                inventory=self.inv_obj,
                variable_manager=self.variable_manager,
                loader=self.loader,
                passwords=self.passwords,
                stdout_callback=self.results_callback,
                # options= options,
            )
            # self.tqm._stdout_callback = self.results_callback
            result = self.tqm.run(play)
        finally:
            if self.tqm:
                self.tqm.cleanup()
                shutil.rmtree(C.DEFAULT_LOCAL_TMP, True)
        return result

    def playbook(self, playbooks, extra_vars={}):
        """
        Keyword arguments:
        playbooks --  需要是一个列表类型
        """

        self.variable_manager.extra_vars.update(extra_vars)
        password = self.passwords
        try:
            playbook_executor = PlaybookExecutor(
                playbooks=playbooks,
                inventory=self.inv_obj,
                variable_manager=self.variable_manager,
                loader=self.loader,
                passwords=password,
            )
            playbook_executor._tqm._stdout_callback = self.results_callback
            result = playbook_executor.run()
        finally:
            if playbook_executor._tqm:
                playbook_executor._tqm.cleanup()
                shutil.rmtree(C.DEFAULT_LOCAL_TMP, True)
        return result

    def get_result(self):
        print("Final status:\n")
        res = {}
        print_status = set()
        for result in self.results_callback.all_info:
            host_ip = result['host_ip']
            status = result['status']
            print_status.add(status)
            if host_ip not in res:
                res[host_ip] = {}
            if status not in res[host_ip]:
                res[host_ip][status] = 1
            res[host_ip][status] += 1
        for host_ip, tasks in self.results_callback.host_results.items():
            for task in tasks:
                status = task['status']
                print_status.add(status)
                if host_ip not in res:
                    res[host_ip] = {}
                if status not in res[host_ip]:
                    res[host_ip][status] = 0
                res[host_ip][status] += 1
        for host_ip in sorted(res.keys()):
            print("{}: ".format(host_ip))
            for status in print_status:
                print("{}: {}".format(status, res[host_ip].get(status, 0)))
        return res

    # 动态添加主机，传入主机列表和组名
    def add_dynamic_hosts(self, hostip_list, groupname=None, groupvars=None):
        """
            add hosts to a group
        """
        # 如果有传入组名，则添加组，并创建Group实例
        if groupname:
            self.inv_obj.add_group(groupname)
            my_group = Group(name=groupname)
        # 如果有传入组变量，则将组变量设置给上面创建的Group实例
        if groupvars:
            for key, value in groupvars.iteritems():
                my_group.set_variable(key, value)

        # add hosts to group
        # 如果有传入组名，则遍历主机列表，添加主机并且设置组
        if groupname:
            for hostip in hostip_list:
                self.inv_obj.add_host(host=hostip, group=groupname)
        # 如果没有传入组名，则遍历主机列表，只添加主机
        else:
            for hostip in hostip_list:
                self.inv_obj.add_host(host=hostip)


def test_connection(
        user,
        password,
        ips,
        vault_file_path,
        vault_file_password, ):
    mas = MyAnsibleService(
        ssh_password=password,
        remote_user=user,
        timeout=1, forks=16,
        inventory=','.join(ips),
        vault_file=vault_file_path,
        vault_password=vault_file_password
    )
    mas.load_vault_file()
    # mas.playbook(playbooks=[ping_vault_playbooks])
    mas.run(
        play_source=DEFAULT_PING_PLAY_SOURCE
    )
    unreachable_ips = mas.results_callback.host.get('unreachable', {}).keys()
    for ip in unreachable_ips:
        if ip not in mas.vault_manager.vault_vars:
            continue
        new_connections = list(filter(
            lambda x: not (x['user'] == user and x['password'] == password),
            mas.vault_manager.vault_vars[ip]['connections']
        ))
        mas.vault_manager.vault_vars[ip]['connections'] = new_connections

    ok_ips = list(
        set(mas.results_callback.host.get('ok', {}).keys())
        - set(mas.results_callback.host.get('unreachable', {}).keys())
    )
    print('Update ip\n')
    for ip in ok_ips:
        print(ip)
    print('update {} ips\n'.format(len(ok_ips)))
    for ip in ok_ips:
        if ip not in mas.vault_manager.vault_vars:
            mas.vault_manager.vault_vars[ip] = {"connections": [{'user': user, 'password': password}]}
        else:
            add_new = True
            for connection in mas.vault_manager.vault_vars[ip]['connections']:
                if connection['user'] == user:
                    add_new = False
                    connection['password'] = password
            if add_new:
                mas.vault_manager.vault_vars[ip]['connections'].append({'user': user, 'password': password})
    mas.vault_manager.encrypt_file()
