# -*- coding: utf-8 -*-
# !/usr/bin/python2
import sqlalchemy
from sqlalchemy.ext.declarative import declarative_base

SQLITE_MEM = declarative_base()
SQLITE_LOCAL = declarative_base()
MYSQL_4226 = declarative_base()
MYSQL_4239 = declarative_base()
CMDB_WRITE = declarative_base()
CMDB_READ = declarative_base()


class Device:
    InstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    CINAME = sqlalchemy.Column(sqlalchemy.String(length=50))
    Category = sqlalchemy.Column(sqlalchemy.String(length=50))
    Type = sqlalchemy.Column(sqlalchemy.String(length=50))
    Item = sqlalchemy.Column(sqlalchemy.String(length=50))
    CIStatus = sqlalchemy.Column(sqlalchemy.String(length=255))
    ManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    ManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    ManagerB = sqlalchemy.Column(sqlalchemy.String(length=50))
    CSOSVersion = sqlalchemy.Column(sqlalchemy.String(length=255))
    HostName = sqlalchemy.Column(sqlalchemy.String(length=255))
    Model = sqlalchemy.Column(sqlalchemy.String(length=50))
    Description = sqlalchemy.Column(sqlalchemy.String(length=255))
    ShortDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    Envir = sqlalchemy.Column(sqlalchemy.String(length=50))
    SerialNumber = sqlalchemy.Column(sqlalchemy.String(length=255))
    VIPAdress = sqlalchemy.Column(sqlalchemy.String(length=255))
    IPAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    IPAddressAll = sqlalchemy.Column(sqlalchemy.String(length=255))
    ManagementAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    ManufacturerName = sqlalchemy.Column(sqlalchemy.String(length=50))
    Building = sqlalchemy.Column(sqlalchemy.String(length=50))
    Cabinet = sqlalchemy.Column(sqlalchemy.String(length=50))
    StartU = sqlalchemy.Column(sqlalchemy.String(length=50))
    EndU = sqlalchemy.Column(sqlalchemy.String(length=50))

    SystemBit = sqlalchemy.Column(sqlalchemy.String(length=63))
    CPUModel = sqlalchemy.Column(sqlalchemy.String(length=255))

    MainStartDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    MainEbdDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    CreateDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    ModifiedDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)

    def __str__(self):
        return 'HostName:{}\tCIStatus:{}\tManagerA:{}\tInstanceID:{}'.format(
            self.HostName, self.CIStatus, self.ManagerA,
            self.InstanceID)

    def __repr__(self):
        return self.__str__()


class Device_CMDB(Device, CMDB_READ):
    __tablename__ = 'SZSE_CMDB_Devicelist'
    __table_args__ = {'schema': 'dbo'}


class Device_MEM(Device, SQLITE_MEM):
    __tablename__ = 'tbl_device'


class App:
    InstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    CINAME = sqlalchemy.Column(sqlalchemy.String(length=50))
    ManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    ManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    CIStatus = sqlalchemy.Column(sqlalchemy.String(length=50))
    APPLevel = sqlalchemy.Column(sqlalchemy.String(length=50))
    AppID = sqlalchemy.Column(sqlalchemy.String(length=50))
    ModifiedDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)


class App_CMDB(App, CMDB_READ):
    __tablename__ = 'SZSE_CMDB_AppList'
    __table_args__ = {'schema': 'dbo'}


class App_MEM(App, SQLITE_MEM):
    __tablename__ = 'tbl_AppList'


# SELECT *
# from SZSE_CMDB_AppList App,
# BMC_CORE_BMC_BaseRelationship Rel,SZSE_CMDB_DeviceList Device
# where
# Rel.DatasetId='BMC.ASSET'
# and Rel.Name='ImpactOnly'
# and Rel.Destination_Classid='BMC_Application'
# and Rel.Source_ClassId='BMC_Computersystem'
# and (Rel.MarkAsDeleted=0 or Rel.MarkAsDeleted is null)
# and App.instanceid=Rel.Destination_InstanceId
# and Device.InstanceID=Rel.Source_InstanceId
class Relationship:
    RequestId = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    DatasetId = sqlalchemy.Column(sqlalchemy.String(length=50))
    Name = sqlalchemy.Column(sqlalchemy.String(length=50))
    Source_ClassId = sqlalchemy.Column(sqlalchemy.String(length=50))
    Destination_ClassId = sqlalchemy.Column(sqlalchemy.String(length=50))
    Source_InstanceId = sqlalchemy.Column(sqlalchemy.String(length=50))
    Destination_InstanceId = sqlalchemy.Column(sqlalchemy.String(length=50))
    MarkAsDeleted = sqlalchemy.Column(sqlalchemy.String(length=50))


class Relationship_CMDB(Relationship, CMDB_READ):
    __tablename__ = 'BMC_CORE_BMC_BaseRelationship'
    __table_args__ = {'schema': 'dbo'}


class AppAndDevice:
    DeviceInstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    DeviceCINAME = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceManagerB = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceItem = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceCSOSVersion = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceCIStatus = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceShortDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceHostName = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceModel = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceIPAddress = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceManagementAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceManufacturerName = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceBuilding = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceType = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceModifiedDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    DeviceCabinet = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceStartU = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceEndU = sqlalchemy.Column(sqlalchemy.String(length=50))

    DeviceIPAddressAll = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceVIPAddress = sqlalchemy.Column(sqlalchemy.String(length=255))

    AppCINAME = sqlalchemy.Column(sqlalchemy.String(length=50))
    AppManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    AppManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    APPLevel = sqlalchemy.Column(sqlalchemy.String(length=50))
    AppCIStatus = sqlalchemy.Column(sqlalchemy.String(length=50))
    AppInstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    AppID = sqlalchemy.Column(sqlalchemy.String(length=50))

    def __init__(self, device_id, device_ciname):
        self.DeviceInstanceID = device_id
        self.DeviceCINAME = device_ciname
        self.DeviceManagerA = None
        self.DeviceCSOSVersion = None
        self.DeviceCIStatus = None
        self.DeviceModel = None
        self.DeviceManagerGroup = None

    def __str__(self):
        return '{}\t{}\t{}\t{}'.format(self.DeviceHostName, self.DeviceInstanceID, self.AppCINAME, self.AppInstanceID)

    def __repr__(self):
        return self.__str__()


class AppAndDevice_CMDB(AppAndDevice, CMDB_READ):
    __tablename__ = 'SZSE_CMDB_AppAndDevice'
    __table_args__ = {'schema': 'dbo'}


class AppAndDevice_MEM(AppAndDevice, SQLITE_MEM):
    __tablename__ = 'tbl_AppAndDevice'


class AppSecurityLevel(CMDB_READ):
    __tablename__ = 'BMC_CORE_BMC_Application'
    __table_args__ = {'schema': 'dbo'}
    aSel_IsContainerApp = sqlalchemy.Column(sqlalchemy.String(length=50))
    aSel_IsInternetApp = sqlalchemy.Column(sqlalchemy.String(length=50))
    aSel_IsPurchaseSys = sqlalchemy.Column(sqlalchemy.String(length=50))
    aChr_EqualPL = sqlalchemy.Column(sqlalchemy.String(length=255))
    aChr_SecurityLevel = sqlalchemy.Column(sqlalchemy.String(length=255))
    name = sqlalchemy.Column(sqlalchemy.String(length=50))
    aChr_SysEngName = sqlalchemy.Column(sqlalchemy.String(length=50))
    Category = sqlalchemy.Column(sqlalchemy.String(length=50))
    RequestId = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True)

    def __str__(self):
        return '{}\t{}\t{}'.format(self.name, self.aChr_SecurityLevel, self.aChr_SysEngName)

    def __repr__(self):
        return self.__str__()


class DeviceDisplay_MYSQL(Device, MYSQL_4226):
    __tablename__ = 'tbl_device_list'
    bmc_setting = sqlalchemy.Column(sqlalchemy.String(length=64))
    baseline_setting = sqlalchemy.Column(sqlalchemy.String(length=256))
    status_modified_date = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    pass


class VitrualMachine(MYSQL_4226):
    __tablename__ = 'VMInfo'
    # id = sqlalchemy.Column(sqlalchemy.String(length=32), primary_key=True, nullable=False)
    name = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True, nullable=False)
    virtualization_platform = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    __mapper_args__ = {
        'polymorphic_on': virtualization_platform
    }

    def __str__(self):
        return 'name: {}, virtualization_platform: {}'.format(self.name, self.virtualization_platform)


class Vmware_VM(VitrualMachine):
    __mapper_args__ = {
        'polymorphic_identity': 'VMware',
    }
    template = sqlalchemy.Column(sqlalchemy.String(length=16))
    vmPathName = sqlalchemy.Column(sqlalchemy.String(length=128))
    memorySizeMB = sqlalchemy.Column(sqlalchemy.Integer)
    cpuReservation = sqlalchemy.Column(sqlalchemy.Integer)
    memoryReservation = sqlalchemy.Column(sqlalchemy.Integer)
    numCpu = sqlalchemy.Column(sqlalchemy.Integer)
    numEthernetCards = sqlalchemy.Column(sqlalchemy.Integer)
    numVirtualDisks = sqlalchemy.Column(sqlalchemy.Integer)
    uuid = sqlalchemy.Column(sqlalchemy.String(length=48))
    instanceUuid = sqlalchemy.Column(sqlalchemy.String(length=48))
    guestId = sqlalchemy.Column(sqlalchemy.String(length=48))
    guestFullName = sqlalchemy.Column(sqlalchemy.String(length=64))
    annotation = sqlalchemy.Column(sqlalchemy.String(length=1024))
    hwVersion = sqlalchemy.Column(sqlalchemy.String(length=16))
    host = sqlalchemy.Column(sqlalchemy.String(length=32))


class HIDSBlackList(MYSQL_4226):
    __tablename__ = 'tbl_hids_blacklist'
    DeviceInstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    DeviceManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceCSOSVersion = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceCIStatus = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceShortDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceHostName = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceIPAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceManagementAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceManufacturerName = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceType = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceModifiedDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    DeviceCabinet = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceStartU = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceEndU = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceVIPAdress = sqlalchemy.Column(sqlalchemy.String(length=50))

    def __str__(self):
        return '{} {}'.format(self.DeviceHostName, self.DeviceInstanceID)

    def __repr__(self):
        return '{} {} {}'.format(self.DeviceHostName, self.DeviceManagerA, self.DeviceIPAddress)

class HIDSExemptionList(MYSQL_4226):
    __tablename__ = 'tbl_hids_exemption_list'
    InstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    CINAME = sqlalchemy.Column(sqlalchemy.String(length=50))
    Category = sqlalchemy.Column(sqlalchemy.String(length=50))
    Type = sqlalchemy.Column(sqlalchemy.String(length=50))
    Item = sqlalchemy.Column(sqlalchemy.String(length=50))
    CIStatus = sqlalchemy.Column(sqlalchemy.String(length=255))
    ManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    ManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    ManagerB = sqlalchemy.Column(sqlalchemy.String(length=50))
    CSOSVersion = sqlalchemy.Column(sqlalchemy.String(length=255))
    HostName = sqlalchemy.Column(sqlalchemy.String(length=255))
    Model = sqlalchemy.Column(sqlalchemy.String(length=50))
    Description = sqlalchemy.Column(sqlalchemy.String(length=255))
    ShortDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    reason = sqlalchemy.Column(sqlalchemy.String(length=100))
    Envir = sqlalchemy.Column(sqlalchemy.String(length=50))
    SearialNumber = sqlalchemy.Column(sqlalchemy.String(length=255))
    VIPAdress = sqlalchemy.Column(sqlalchemy.String(length=255))
    IPAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    IPAddressAll = sqlalchemy.Column(sqlalchemy.String(length=255))
    ManagementAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    ManufacturerName = sqlalchemy.Column(sqlalchemy.String(length=50))
    Building = sqlalchemy.Column(sqlalchemy.String(length=50))
    Cabinet = sqlalchemy.Column(sqlalchemy.String(length=50))
    StartU = sqlalchemy.Column(sqlalchemy.String(length=50))
    EndU = sqlalchemy.Column(sqlalchemy.String(length=50))
    SystemBit = sqlalchemy.Column(sqlalchemy.String(length=63))
    CPUModel = sqlalchemy.Column(sqlalchemy.String(length=255))
    ModifiedDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP(True))
    MainStartDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP(True))
    MainEbdDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP(True))
    CreateDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP(True))

    def __str__(self):
        return '{} {}'.format(self.HostName, self.reason)

    def __repr__(self):
        return '{} {}'.format(self.HostName, self.reason)

class HIDSExemptionListNew(MYSQL_4226):
    __tablename__ = 'tbl_hids_exemption_list_new'
    DeviceInstanceID = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True, nullable=True)
    DeviceManagerA = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceCSOSVersion = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceCIStatus = sqlalchemy.Column(sqlalchemy.String(length=255))
    #DeviceDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceShortDescription = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceHostName = sqlalchemy.Column(sqlalchemy.String(length=255))
    DeviceManagerGroup = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceIPAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    #DeviceManagementAddress = sqlalchemy.Column(sqlalchemy.String(length=255))
    #DeviceManufacturerName = sqlalchemy.Column(sqlalchemy.String(length=50))
    DeviceType = sqlalchemy.Column(sqlalchemy.String(length=50))
    #DeviceModifiedDate = sqlalchemy.Column(sqlalchemy.TIMESTAMP)
    DeviceCabinet = sqlalchemy.Column(sqlalchemy.String(length=50))
    #DeviceStartU = sqlalchemy.Column(sqlalchemy.String(length=50))
    #DeviceEndU = sqlalchemy.Column(sqlalchemy.String(length=50))
    #DeviceVIPAdress = sqlalchemy.Column(sqlalchemy.String(length=50))
    Reason = sqlalchemy.Column(sqlalchemy.String(length=50))

    def __str__(self):
        return '{} {}'.format(self.DeviceHostName, self.DeviceInstanceID)

    def __repr__(self):
        return '{} {} {}'.format(self.DeviceHostName, self.DeviceManagerA, self.DeviceIPAddress)

# class HIDSExemptionList(Device, MYSQL_4226):
#     __tablename__ = 'tbl_hids_exemption_list'
#     reason = sqlalchemy.Column(sqlalchemy.String(length=100))

#     def __str__(self):
#         return '{} {}'.format(self.HostName, self.InstanceID)

#     def __repr__(self):
#         return '{} {} {}'.format(self.HostName, self.ManagerA, self.IPAddress)
