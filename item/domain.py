# -*- coding: utf-8 -*-
# !/usr/bin/python2


import sqlalchemy

from item import SQLITE_MEM, CMDB_WRITE


class ModelSangforVipPool(SQLITE_MEM):
    __tablename__ = 'tbl_sangfor_vip_pool'
    name = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    state = sqlalchemy.Column(sqlalchemy.String(length=10))
    vs_ip = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)
    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10))

    def __str__(self):
        return 'name: {}\tstate: {}\tvs_ip: {}'.format(self.name, self.state, self.vs_ip)

    def __repr__(self):
        return self.__str__()


class ModelSangforDns(SQLITE_MEM):
    __tablename__ = 'tbl_sangfor_dns'
    name = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    state = sqlalchemy.Column(sqlalchemy.String(length=10))
    domain = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)
    vip_pool = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)
    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10))

    def __str__(self):
        return 'name: {}\tstate: {}\tdomain: {}\tvip_pool: {}'.format(self.name, self.state, self.domain, self.vip_pool)

    def __repr__(self):
        return self.__str__()


class ModelSangforVS(SQLITE_MEM):
    __tablename__ = 'tbl_sangfor_vs'
    name = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    state = sqlalchemy.Column(sqlalchemy.String(length=10))
    node_pool = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)
    vip = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)
    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10))

    def __str__(self):
        return 'name: {}\tstate: {}\tvip: {}\tnode_pool: {}'.format(self.name, self.state, self.vip, self.node_pool)

    def __repr__(self):
        return self.__str__()


class ModelSangforNode(SQLITE_MEM):
    __tablename__ = 'tbl_sangfor_node'
    name = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    state = sqlalchemy.Column(sqlalchemy.String(length=10))
    node_ip = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)
    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10))

    def __str__(self):
        return 'name: {}\tstate: {}\tnode_ip: {}'.format(self.name, self.state, self.node_ip)

    def __repr__(self):
        return self.__str__()


class ModelF5VS(SQLITE_MEM):
    __tablename__ = 'tbl_f5_vs'
    domain = sqlalchemy.Column(sqlalchemy.String(length=50))
    domain_state = sqlalchemy.Column(sqlalchemy.String(length=8))

    name = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True)
    vs_ip = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True)
    vs_pool = sqlalchemy.Column(sqlalchemy.String(length=50), primary_key=True)

    vs_enable = sqlalchemy.Column(sqlalchemy.String(length=50))
    vs_avail = sqlalchemy.Column(sqlalchemy.String(length=50))

    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)

    def __str__(self):
        return 'name: {}\tvs_pool: {}\tvs_ip: {}'.format(self.name, self.vs_pool, self.vs_ip)

    def __repr__(self):
        return self.__str__()


class ModelF5Node(SQLITE_MEM):
    __tablename__ = 'tbl_f5_node'
    node_ip = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    vs_pool = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    node_enable = sqlalchemy.Column(sqlalchemy.String(length=8))
    node_avail = sqlalchemy.Column(sqlalchemy.String(length=8))
    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10), primary_key=True)

    def __str__(self):
        return 'vs_pool: {}\tstate: {}\tnode_ip: {}'.format(self.vs_pool, self.node_avail, self.node_ip)

    def __repr__(self):
        return self.__str__()


class ModelWindowsDNS(SQLITE_MEM):
    hostname = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    record_class = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    record_type = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    record_data = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    zone_name = sqlalchemy.Column(sqlalchemy.String(length=20), primary_key=True, nullable=False)
    source_hostname = sqlalchemy.Column(sqlalchemy.String(length=10))

    def __str__(self):
        return 'hostname: {}\trecord_data: {}\tzone_name: {}'.format(self.hostname, self.record_data, self.zone_name)

    def __repr__(self):
        return self.__str__()


class ModelDomainRow:
    domain = sqlalchemy.Column(sqlalchemy.String(length=32), primary_key=True, nullable=True)
    domain_state = sqlalchemy.Column(sqlalchemy.String(length=8))
    external_ip = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True)
    external_ip_state = sqlalchemy.Column(sqlalchemy.String(length=8))
    internal_vip = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True)
    internal_vip_state = sqlalchemy.Column(sqlalchemy.String(length=8))
    internal_ip = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True)
    internal_ip_state = sqlalchemy.Column(sqlalchemy.String(length=8))
    external_source = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True)
    internal_source = sqlalchemy.Column(sqlalchemy.String(length=64), primary_key=True)

    def __str__(self):
        return 'domain: {}\texternal_ip: {}\tinternal_vip: {}\tinternal_ip: {}'.format(
            self.domain, self.external_ip, self.internal_vip, self.internal_ip
        )

    def __repr__(self):
        return self.__str__()


class CMDBModelDomainRow(ModelDomainRow, CMDB_WRITE):
    pass
