# Ansible-vault


# my_ansible.__init__.py 使用方式

首先创建一个空的vault文件用于存储服务器的用户名和密码

vault解密后格式如下

```json
{
  "*******": {
    "connections": [
      {
        "password": "abcdABCD",
        "user": "root"
      },
      {
        "password": "abcdABCD",
        "user": "user1"
      }
    ]
  }
}
```

使用对应的playbook用于获取此类vault中的服务器用户和密码

```yaml
---
- name: playbook名字
  hosts: all
  gather_facts: no
  vars:
    # 默认的使用用户，即默认使用的vault中对应IP的存储的用户
    - default_user: user
  vars_files:
    # 使用的vault文件地址，这里是假设为test-vault
    - test-vault
  tasks:
    - name: Find and set connection
      # ansible 赋值模块，这里是把以下命令赋值给select_connection
      set_fact:
        # 解析vault中的数据，把对应IP的connections的用户为default_user的connection提取出来，赋值给select_connection
        # 没有就使用第一个存储的connection
        select_connection: >
          {{
            (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list | first)
            if (vars[inventory_hostname].connections
            | selectattr('user', 'equalto', default_user) | list) | length > 0
            else vars[inventory_hostname].connections[0]
          }}
    - name: set fact from connection
      set_fact:
        # 使用用户和密码
        ansible_user: "{{ select_connection.user }}"
        ansible_ssh_pass: "{{ select_connection.password }}"
    - name: 其他play
      ping:
```

test-vault 可以通过以下方式创建

```shell

# 首先创建一个新的ansible-vault用于存储服务器的用户名、密码
ansible-vault create test-vault
# 这时候ansible会要求输入待创建的vault密码


# 编辑该文件，文件内容解密后应为一个空字典： {}，用于my_ansible的package初始解析
ansible-vault edit test-vault
```

然后通过`my_ansible.test_connection`探测对应的服务器的密码，如

```py
from my_ansible import *
var_file= 'test_vault'
vault_pass = 'vault_password'
test_connection(
    user='user', password='abcdABCD', 
    ips=['*******','*******'], 
    vault_file_path=var_file, vault_file_password=vault_pass
)
```

如果密码正确，运行上述代码后，test-vault解密后内容应为

```json
{
  "*******": {
    "connections": [
      {
        "password": "abcdABCD",
        "user": "user"
      }
    ]
  },
  "*******": {
    "connections": [
      {
        "password": "abcdABCD",
        "user": "user"
      }
    ]
  }
}
```
# my_ansible调用方式

```py
# -*- coding: utf-8 -*-
# !/usr/bin/python
import sys

if sys.version_info[0] < 3:
    reload(sys)
    sys.setdefaultencoding('utf-8')
else:
    import importlib
    importlib.reload(sys)

sys.path.append('../')

from my_ansible import *
import getpass

if __name__ == '__main__':
    # 指定使用的playbook，保存密码变量的vault文件
    playbook_path = 'playbook.yml'
    var_file = 'my_vault'
    
    # 指定批量运行的IP
    ips = ['*******', '*******']

    # 输入vault密码
    vault_pass = getpass.getpass('Plesa input Vaultpass: ')
    
    # 实例一个ansible服务并运行playbook
    mas = MyAnsibleService(
        vault_pass=vault_pass,
        var_file=var_file,
        timeout=1,
        inventory=','.join(ips),
        become_user='root', become_method='sudo'
    )
    mas.playbook(playbooks=[playbook_path])
    
    # ips保存没有正确运行playbook中任务的IP
    ips = sorted(list(set(mas.results_callback.host.get('failed', {}).keys()) | set(
        mas.results_callback.host.get('unreachable', {}).keys())))

    for task in mas.results_callback.all_info:
        if task['result_info'].get('msg'):
            print(task['host_ip'])
            print(task['result_info'].get('msg'))
            
        if ['task_name'] == 'show answer':
            ip_ans = json.loads(task['result_info']['stdout_lines'][0])

```